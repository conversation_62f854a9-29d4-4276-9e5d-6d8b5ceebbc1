# Solução para Problemas de CORS e Cross-Origin-Opener-Policy

## Problemas Identificados

### 1. Cross-Origin-Opener-Policy (COOP)
```
Cross-Origin-Opener-Policy policy would block the window.closed call.
Cross-Origin-Opener-Policy policy would block the window.close call.
```

### 2. CORS na API Flow
```
Access to fetch at 'https://flow.ciandt.com/auth-engine-api/v1/api-key/token'
from origin 'https://flow-adapted.web.app' has been blocked by CORS policy
```

## Soluções Implementadas

### 1. Headers de Segurança no Firebase Hosting

Configurado no `firebase.json`:
```json
"headers": [
  {
    "source": "**",
    "headers": [
      {
        "key": "Cross-Origin-Opener-Policy",
        "value": "same-origin-allow-popups"
      },
      {
        "key": "Cross-Origin-Embedder-Policy",
        "value": "unsafe-none"
      }
    ]
  }
]
```

### 2. Service Worker como Proxy CORS

**Solução sem custos adicionais** - Implementamos um Service Worker que intercepta requisições para a API Flow e resolve problemas de CORS:

#### Service Worker (`public/sw.js`)
- **Intercepta**: Todas as requisições para `flow.ciandt.com`
- **Modifica**: Headers das requisições e respostas
- **Adiciona**: Headers CORS apropriados
- **Resolve**: Problemas de CORS sem servidor adicional

#### Registro Automático
- **Desenvolvimento**: Service Worker não é registrado (usa proxy Vite)
- **Produção**: Service Worker é registrado automaticamente

### 3. Atualização do FlowService

O `FlowService` foi modificado para:
- **Desenvolvimento**: Usar proxy do Vite (`/flow-api`)
- **Produção**: Usar URL direta da Flow (Service Worker intercepta)

```typescript
if (isDevelopment) {
  baseUrl = '/flow-api';
  authBaseUrl = '/flow-api';
} else {
  baseUrl = 'https://flow.ciandt.com';
  authBaseUrl = 'https://flow.ciandt.com';
}
```

## Como Implementar

### 1. Deploy Simples (Apenas Hosting)
```bash
npm run build:prod
firebase deploy --only hosting
```

### 2. Verificar Service Worker
- Abrir DevTools > Application > Service Workers
- Verificar se o Service Worker está registrado e ativo

## Estrutura de Arquivos

```
├── firebase.json              # Configuração com headers
├── public/
│   └── sw.js                 # Service Worker para proxy CORS
├── src/
│   ├── services/
│   │   ├── serviceWorker.ts  # Registro do Service Worker
│   │   └── flowService.ts    # Atualizado para usar Service Worker
│   └── main.tsx              # Registro automático em produção
```

## Como Funciona

1. **Desenvolvimento**: Proxy do Vite resolve CORS
2. **Produção**: Service Worker intercepta requisições
3. **Transparente**: Código da aplicação não muda

## Benefícios

1. **Sem Custos**: Não requer plano pago do Firebase
2. **Resolve CORS**: Service Worker adiciona headers necessários
3. **Mantém Segurança**: Headers apropriados configurados
4. **Compatibilidade**: Funciona em dev e produção
5. **Transparente**: Não requer mudanças no código cliente

## Monitoramento

Para verificar se está funcionando:

1. **Console do Browser**: Não deve mais mostrar erros de CORS
2. **Network Tab**: Requisições devem ir para `/api/flow/*`
3. **Firebase Console**: Logs das functions mostram atividade

## Troubleshooting

### Se ainda houver erros de CORS:
1. Verificar se as functions foram deployadas
2. Confirmar que os rewrites estão corretos no `firebase.json`
3. Limpar cache do browser

### Se as functions não funcionarem:
1. Verificar logs no Firebase Console
2. Testar localmente com `npm run serve:functions`
3. Confirmar que as dependências foram instaladas
