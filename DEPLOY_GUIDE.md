# 🚀 Guia de Deploy - Adaptive Learning Platform

## ✅ Pré-requisitos Concluídos

- ✅ Firebase CLI instalado (v14.6.0)
- ✅ Build testado e funcionando
- ✅ Scripts de deploy criados
- ✅ Configuração do Firebase pronta

## 🎯 Deploy em 3 Passos Simples

### 1. Login no Firebase
```bash
firebase login
```
**Isso abrirá o navegador para você fazer login com sua conta Google.**

### 2. Deploy <PERSON><PERSON>o (Recomendado)
```bash
npm run deploy:quick
```

### 3. Deploy Completo (Com testes)
```bash
npm run deploy
```

## 🌐 URLs da Aplicação

- **Produção**: https://flow-adapted.web.app
- **Console Firebase**: https://console.firebase.google.com/project/flow-adapted

## 📋 Scripts Disponíveis

| Script | Comando | Descrição |
|--------|---------|-----------|
| Deploy Rápido | `npm run deploy:quick` | Build + Deploy direto |
| Deploy Completo | `npm run deploy` | Lint + Test + Build + Deploy |
| Build Produção | `npm run build:prod` | Build otimizado para produção |
| Preview Local | `npm run preview` | Testar build localmente |

## 🔧 Configurações de Produção

### Variáveis de Ambiente (.env.production)
```env
# Firebase
VITE_FIREBASE_PROJECT_ID=flow-adapted
VITE_FIREBASE_API_KEY=AIzaSyDZslYppYPLk3OjYguI0syAcO2mv1eKodM

# Flow Provider
VITE_FLOW_BASE_URL=https://flow.ciandt.com
VITE_FLOW_TENANT=cit
VITE_FLOW_CLIENT_ID=0730f539-b08b-419b-a2f4-b07becad0c9a

# Produção
NODE_ENV=production
VITE_APP_VERSION=1.0.0
```

## 🚨 Importante: Flow Client Secret

⚠️ **O VITE_FLOW_CLIENT_SECRET está vazio no .env.local**

Você precisa:
1. Obter o client secret correto
2. Adicionar em `.env.production`
3. **NUNCA** commitar o secret no Git

## 🔍 Verificações Pós-Deploy

Após o deploy, verifique:

1. **Aplicação carrega**: https://flow-adapted.web.app
2. **Login funciona**: Teste autenticação Firebase
3. **Chat funciona**: Teste com modelo o3-mini
4. **Configurações**: Verifique Flow Provider

## 🐛 Troubleshooting

### Erro de Login Firebase
```bash
firebase logout
firebase login --reauth
```

### Erro de Permissões
```bash
firebase projects:list
# Verifique se flow-adapted aparece na lista
```

### Erro de Build
```bash
rm -rf dist node_modules/.vite
npm ci
npm run build
```

## 🚀 Deploy Automático (CI/CD)

Para configurar deploy automático no futuro:

1. **GitHub Actions**: `.github/workflows/deploy.yml`
2. **Vercel**: Conectar repositório
3. **Netlify**: Conectar repositório

## 📊 Métricas de Performance

Após deploy, monitore:
- **Core Web Vitals**
- **Firebase Analytics**
- **Console do navegador** para erros

## 🎉 Próximos Passos

1. **Domínio customizado**: Configurar DNS
2. **SSL**: Automático via Firebase
3. **CDN**: Automático via Firebase
4. **Monitoramento**: Firebase Performance

---

## 🚀 COMANDO PARA DEPLOY AGORA:

```bash
# 1. Login (uma vez só)
firebase login

# 2. Deploy rápido
npm run deploy:quick
```

**Tempo estimado: 2-3 minutos** ⚡
