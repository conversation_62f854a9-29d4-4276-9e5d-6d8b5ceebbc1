# 🚀 DEPLOY AGORA - 3 Comandos Simples

## ⚡ Deploy em 3 Minutos

### 1. Login Firebase (Uma vez só)
```bash
firebase login
```
*Isso abrirá o navegador - faça login com sua conta Google*

### 2. Deploy R<PERSON>pido
```bash
npm run deploy:quick
```

### 3. Verificar
Acesse: **https://flow-adapted.web.app**

---

## 🔥 Se der erro, use o deploy completo:

```bash
npm run deploy
```

---

## ✅ Tudo Pronto!

- ✅ Build funcionando
- ✅ Firebase configurado  
- ✅ Scripts criados
- ✅ Variáveis de ambiente configuradas

**Só falta fazer login e deploy!** 🚀

---

## 🆘 Se precisar de ajuda:

1. **Erro de login**: `firebase logout && firebase login`
2. **Erro de build**: `rm -rf dist && npm run build`
3. **Erro de permissão**: Verifique se tem acesso ao projeto `flow-adapted`

## 📱 URLs Importantes:

- **App**: https://flow-adapted.web.app
- **Console**: https://console.firebase.google.com/project/flow-adapted
