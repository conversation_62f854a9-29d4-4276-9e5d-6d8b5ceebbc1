# 🔧 Configuração do Flow - Guia do Usuário

## So<PERSON> as Credenciais do Flow

Este sistema foi projetado para que **cada usuário configure suas próprias credenciais do Flow**. Isso garante:

- ✅ **Segurança**: Suas credenciais ficam privadas e criptografadas
- ✅ **Personalização**: Cada usuário pode usar suas próprias configurações
- ✅ **Isolamento**: Não há compartilhamento de credenciais entre usuários

## Como Configurar

### 1. **Primeiro Acesso**
Quando você fizer login pela primeira vez, um modal de configuração aparecerá automaticamente solicitando suas credenciais do Flow.

### 2. **Configuração Manual**
Você pode acessar as configurações a qualquer momento através de:
- **Botão de configurações** no cabeçalho (ícone de engrenagem)
- **<PERSON><PERSON><PERSON> "Configurar Flow"** na página inicial (se não configurado)

### 3. **Credenciais Necessárias**

Para usar o sistema, você precisa fornecer:

| Campo | Descrição | Obrigatório |
|-------|-----------|-------------|
| **Base URL** | URL da API do Flow | ✅ |
| **Tenant** | Identificador do tenant | ✅ |
| **Client ID** | ID do cliente para autenticação | ✅ |
| **Client Secret** | Chave secreta do cliente | ✅ |
| **App to Access** | Aplicação a ser acessada | ✅ |

### 4. **Valores Padrão**

O sistema vem com apenas alguns valores pré-configurados para facilitar:
- **Base URL**: `https://flow.ciandt.com`
- **App to Access**: `llm-api`

**⚠️ Importante**: Você deve fornecer suas credenciais pessoais:
- **Tenant**: Seu tenant específico
- **Client ID**: Seu ID de cliente pessoal
- **Client Secret**: Sua chave secreta pessoal

## Como Obter suas Credenciais

### 🔗 Link Direto para Credenciais:
**[https://flow.ciandt.com/settings/api-keys](https://flow.ciandt.com/settings/api-keys)**

### Para usuários da CI&T:
1. **Acesse**: [flow.ciandt.com/settings/api-keys](https://flow.ciandt.com/settings/api-keys)
2. **Faça login** com suas credenciais corporativas
3. **Gere ou copie** seu **Client ID** e **Client Secret**
4. **Anote seu Tenant** (geralmente `cit` para CI&T)

### Para outros usuários:
1. Entre em contato com o administrador do Flow da sua organização
2. Solicite credenciais de acesso à API
3. Certifique-se de ter permissões para acessar os modelos LLM
4. Obtenha o link específico para sua organização

## Testando a Configuração

Após inserir suas credenciais:

1. **Clique em "🔗 Testar"** para verificar a conectividade
2. O sistema tentará listar os modelos disponíveis
3. Se bem-sucedido, você verá uma mensagem de confirmação
4. **Clique em "Salvar"** para armazenar as configurações

## Resolução de Problemas

### ❌ Erro 401 Unauthorized
- **Causa**: Client ID ou Client Secret incorretos
- **Solução**: Verifique suas credenciais no portal do Flow

### ❌ Erro de Conexão
- **Causa**: URL incorreta ou problemas de rede
- **Solução**: Verifique a Base URL e sua conexão com a internet

### ❌ Erro de Tenant
- **Causa**: Tenant incorreto ou sem permissões
- **Solução**: Confirme o tenant correto com seu administrador

## Segurança

### 🔒 Como suas credenciais são protegidas:
- Armazenadas criptografadas no Firebase
- Nunca expostas no código-fonte
- Isoladas por usuário
- Transmitidas apenas via HTTPS

### 🚫 O que NÃO fazer:
- Não compartilhe suas credenciais
- Não as coloque em arquivos de configuração
- Não as envie por email ou chat

## Suporte

Se você encontrar problemas:

1. **Verifique os logs do navegador** (F12 → Console)
2. **Teste suas credenciais** no portal oficial do Flow
3. **Entre em contato** com o suporte técnico da sua organização

---

**💡 Dica**: Mantenha suas credenciais sempre atualizadas e seguras!
