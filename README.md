# Adapted - Plataforma de Aprendizado Adaptativo

Plataforma que utiliza inteligência artificial para fornecer uma experiência personalizada de aprendizado através da API Flow, com suporte completo a diferentes personas cognitivas (ADHD, Autismo, Dislexia).

## 🎯 Visão Geral

Adapted é uma aplicação web React que se integra com a API Flow para fornecer recursos de IA em um ambiente de aprendizado personalizado. A aplicação utiliza autenticação Firebase, armazena dados no Firestore e implementa um sistema avançado de temas adaptativos baseados em necessidades cognitivas específicas.

## 🚀 Tecnologias

- **Frontend**: React 18 + TypeScript
- **Build Tool**: Vite com React plugin
- **Estilização**: CSS Variables + Tailwind CSS (configuração customizada)
- **Autenticação**: Firebase Authentication
- **Banco de Dados**: Firestore
- **Testing**: Vitest + React Testing Library + jsdom
- **Qualidade de Código**: ESLint + Prettier (máximo 0 warnings)
- **AI Integration**: Flow API orchestration platform

## 📁 Estrutura do Projeto

```
adapted/
├── public/              # Ativos públicos
├── src/                 # Código-fonte
│   ├── app/             # Configuração principal da aplicação
│   │   ├── providers/   # Providers globais da aplicação
│   │   └── router.tsx   # Configuração de rotas
│   ├── components/      # Componentes React reutilizáveis
│   │   └── ui/          # Componentes de UI básicos com sistema de temas
│   ├── features/        # Módulos funcionais organizados por domínio
│   │   ├── auth/        # Funcionalidades de autenticação
│   │   ├── flow/        # Integração com Flow API
│   │   └── settings/    # Configurações da aplicação
│   ├── hooks/           # Hooks React personalizados
│   ├── lib/             # Utilitários, helpers e configurações
│   ├── services/        # Serviços e adaptadores para APIs externas
│   ├── contexts/        # Contextos React (Theme, Auth, Flow)
│   ├── types/           # Definições de tipos TypeScript
│   └── tests/           # Testes automatizados
├── docs/                # Documentação adicional (será removida)
├── scripts/             # Scripts de validação e utilitários
├── CLAUDE.md           # Instruções específicas para Claude Code
└── README.md           # Este arquivo
```

## 🛠️ Pré-requisitos

- Node.js 16+
- Conta Firebase com projeto configurado
- Credenciais da API Flow

## ⚙️ Configuração

### 1. Instalação

```bash
git clone [url-do-repositorio]
cd adapted
npm install
```

### 2. Variáveis de Ambiente

Crie um arquivo `.env.local` na raiz do projeto:

```env
# Firebase
VITE_FIREBASE_API_KEY=sua-api-key
VITE_FIREBASE_AUTH_DOMAIN=seu-projeto.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=seu-projeto
VITE_FIREBASE_STORAGE_BUCKET=seu-projeto.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=seu-sender-id
VITE_FIREBASE_APP_ID=seu-app-id

# Flow API
VITE_FLOW_BASE_URL=https://api.flow-url.com
VITE_FLOW_TENANT=tenant-id
VITE_FLOW_CLIENT_ID=client-id
VITE_FLOW_CLIENT_SECRET=client-secret
VITE_FLOW_APP_TO_ACCESS=app-id
VITE_FLOW_AGENT=agent-id
VITE_FLOW_AUTH_BASE_URL=https://auth.flow-url.com

# Opcional - Google Analytics
VITE_GA_ID=G-XXXXXXXXXX

# Opcional - Endpoint para logs remotos
VITE_LOG_ENDPOINT=https://seu-endpoint/logs
```

## 🎮 Comandos Disponíveis

### Desenvolvimento
- `npm run dev` - Inicia o servidor de desenvolvimento
- `npm run build` - Gera a versão de produção
- `npm run preview` - Visualiza a versão de produção localmente

### Qualidade de Código
- `npm run lint` - Verifica o código com ESLint (máximo 0 warnings)
- `npm run format` - Formata o código com Prettier
- `npm test` - Executa os testes com Vitest
- `npm run test:watch` - Executa os testes em modo de observação

**⚠️ IMPORTANTE**: Execute sempre `npm run lint` e `npm test` após fazer alterações.

## 🏗️ Arquitetura

A aplicação segue uma arquitetura em camadas com separação clara de responsabilidades, inspirada em Clean Architecture:

### Camadas Arquiteturais

1. **Apresentação** (`/src/components`, `/src/features/*/pages`)
   - Componentes React funcionais com hooks
   - Organização baseada em features
   - Componentes UI reutilizáveis

2. **Aplicação** (`/src/hooks`, `/src/app/providers`)
   - Hooks customizados para lógica de negócio
   - Providers de contexto para estado global
   - Configuração do roteador React

3. **Domínio** (`/src/types`, `/src/lib`)
   - Definições TypeScript
   - Lógica de negócio e utilitários
   - Sistema de logging

4. **Infraestrutura** (`/src/services`)
   - Integração Firebase (Auth, Firestore)
   - Flow API service para interações IA
   - Adaptadores para APIs externas

### Gerenciamento de Estado

- **Local**: `useState` e `useReducer` para estado de componente
- **Global**: React Context API com providers (`AuthProvider`, `FlowProvider`, `ThemeProvider`)
- **Persistência**: Firestore para dados do usuário, localStorage para preferências

## 🎨 Sistema de Temas Adaptativos

### Personas Cognitivas Suportadas

#### **ADHD Focus**
- **Cores**: Azul vibrante + verde esmeralda
- **UI**: Botões maiores (44px), elementos reduzidos por tela, animações reduzidas
- **Objetivo**: Melhorar foco e engajamento

#### **Autism Structured**
- **Cores**: Cinza neutro + roxo suave
- **UI**: Bordas menores, sem animações, layout consistente
- **Objetivo**: Fornecer previsibilidade e estrutura clara

#### **Dyslexia Readable**
- **Cores**: Verde + âmbar com alto contraste
- **UI**: Fonte OpenDyslexic, espaçamento aumentado, texto maior
- **Objetivo**: Maximizar legibilidade e compreensão

### Implementação Técnica

O sistema utiliza CSS Variables (Custom Properties) para permitir mudanças dinâmicas:

```css
:root {
  --color-primary: 59, 130, 246;
  --background-primary: 255, 255, 255;
}

[data-persona="ADHD"] {
  --color-primary: 59, 130, 246;
  --background-primary: 248, 250, 252;
}
```

### Classes Utilitárias Disponíveis

- **Backgrounds**: `bg-theme-primary`, `bg-theme-secondary`
- **Textos**: `text-theme-primary`, `text-theme-secondary`
- **Botões**: `btn-primary`, `btn-secondary`, `btn-accent`
- **Cards/Inputs**: `card-theme`, `input-theme`

### Uso do Context de Tema

```tsx
import { useTheme } from '@/contexts/ThemeContext';

function MyComponent() {
  const { theme, persona, setPersona, toggleTheme } = useTheme();
  
  return (
    <div className="bg-theme-primary">
      <Button variant="primary" onClick={() => setPersona('ADHD')}>
        Modo ADHD
      </Button>
    </div>
  );
}
```

## 🔌 Integração com Flow API

### Componentes Principais

- **FlowProvider** (`/src/components/FlowProvider.tsx`) - Context React para estado Flow
- **FlowService** (`/src/services/flowService.ts`) - Integração core da API
- **FlowStorageService** (`/src/services/flowStorageService.ts`) - Camada de persistência

### Configuração Requerida

```env
VITE_FLOW_BASE_URL=https://api.flow-url.com
VITE_FLOW_TENANT=tenant-id
VITE_FLOW_CLIENT_ID=client-id
VITE_FLOW_CLIENT_SECRET=client-secret
VITE_FLOW_APP_TO_ACCESS=app-id
VITE_FLOW_AGENT=agent-id
VITE_FLOW_AUTH_BASE_URL=https://auth.flow-url.com
```

## 🧪 Testes

### Estratégia de Testes

- **Unitários**: Componentes individuais e funções utilitárias  
- **Integração**: Fluxos de autenticação e configuração
- **Sistema**: Validação completa do sistema de temas

### Executando Testes

```bash
# Todos os testes
npm test

# Testes específicos
npm test src/tests/theme-system.test.ts
npm test src/tests/config-validation.test.ts
npm test src/tests/flow-connectivity.test.ts

# Modo watch
npm run test:watch
```

## 📊 Observabilidade

### Sistema de Logging

- Sistema centralizado via `logger.ts`
- Níveis configuráveis (debug, info, warn, error)
- Suporte a logs remotos e locais

### Monitoramento

- **Performance**: Web Vitals para métricas (CLS, LCP, FID, TTFB, INP)
- **Erros**: ErrorBoundary para captura de erros não tratados
- **Analytics**: Integração opcional com Google Analytics

## 🔒 Convenções de Código

### Nomenclatura
- **Variáveis/Funções**: camelCase
- **Componentes/Interfaces**: PascalCase
- **Arquivos**: PascalCase para componentes, camelCase para utilitários

### Imports
1. Módulos externos (React, bibliotecas)
2. Módulos internos (utilitários, services)
3. Imports relativos (componentes locais)

### Componentização
- Componentes funcionais com hooks
- Props tipadas com TypeScript
- ErrorBoundary para tratamento de erros

## 🚀 Deploy e CI/CD

### Pipeline Automatizado

1. **Lint e Testes**: Execução automática em PRs
2. **Build**: Geração de versão de produção
3. **Preview**: Deploy automático para ambiente de preview
4. **Produção**: Deploy automático na branch main

### Preparação para Deploy

```bash
# Validar antes do deploy
npm run lint
npm test
npm run build
```

## 🤝 Contribuição

### Processo de Contribuição

1. Fork do projeto
2. Criar branch para feature (`git checkout -b feature/nome-da-feature`)
3. Commit das alterações (`git commit -m 'Adiciona nova feature'`)
4. Push para repositório remoto (`git push origin feature/nome-da-feature`)
5. Abrir Pull Request

### Checklist para PRs

- [ ] Testes passando (`npm test`)
- [ ] Lint sem warnings (`npm run lint`)
- [ ] Build funcionando (`npm run build`)
- [ ] Todas as personas testadas
- [ ] Documentação atualizada se necessário

## 🎯 Próximas Melhorias

- [ ] Suporte a temas personalizados por usuário
- [ ] Mais opções de acessibilidade
- [ ] Otimização de performance
- [ ] Expansão de testes automatizados
- [ ] Implementação de PWA

## 🐛 Troubleshooting

### Problemas Comuns

**Cores não aplicando**
- Verificar se `data-theme` e `data-persona` estão no `<html>`
- Confirmar carregamento das CSS variables

**Erros de configuração Flow**
- Validar todas as variáveis de ambiente
- Executar `scripts/validate-config.js`

**Problemas de build**
- Limpar cache: `rm -rf node_modules/.vite`
- Reinstalar dependências: `npm ci`

## 🚀 Deploy

### Deploy Rápido (3 comandos)

```bash
# 1. Login Firebase (uma vez só)
firebase login

# 2. Verificar se está tudo pronto
npm run deploy:check

# 3. Deploy!
npm run deploy:quick
```

**URL da aplicação**: https://flow-adapted.web.app

### Scripts de Deploy

| Script | Comando | Descrição |
|--------|---------|-----------|
| Verificação | `npm run deploy:check` | Verifica se está tudo pronto |
| Deploy Rápido | `npm run deploy:quick` | Build + Deploy (2-3 min) |
| Deploy Completo | `npm run deploy` | Lint + Test + Build + Deploy |

### 🔧 Service Worker (Proxy CORS)

Para resolver problemas de CORS com a API Flow, implementamos um Service Worker que:

- **Intercepta**: Requisições para `flow.ciandt.com`
- **Adiciona**: Headers CORS apropriados
- **Resolve**: Problemas de CORS sem custos adicionais

**Ativação automática**: O Service Worker é registrado automaticamente em produção.

### Guias Detalhados

- 📋 **Guia Completo**: `DEPLOY_GUIDE.md`
- ⚡ **Deploy Rápido**: `DEPLOY_NOW.md`

## 📄 Licença

Este é um projeto privado. Todos os direitos reservados.

---

**Última atualização**: Dezembro 2025
**Versão da documentação**: 2.1