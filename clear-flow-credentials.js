/**
 * Script para limpar credenciais do Flow do Firebase
 * Execute este script no console do navegador (F12) quando estiver logado na aplicação
 */

// Função para limpar credenciais do Flow
async function clearFlowCredentials() {
  try {
    // Verificar se o Firebase está disponível
    if (typeof window.firebase === 'undefined') {
      console.error('❌ Firebase não está disponível. Certifique-se de estar na aplicação.');
      return;
    }

    // Obter o usuário atual
    const auth = window.firebase.auth();
    const user = auth.currentUser;
    
    if (!user) {
      console.error('❌ Usuário não está logado. Faça login primeiro.');
      return;
    }

    console.log('🔧 Limpando credenciais do Flow para usuário:', user.uid);

    // Obter referência do Firestore
    const db = window.firebase.firestore();
    const configRef = db.collection('flowConfigs').doc(user.uid);

    // Deletar o documento de configuração
    await configRef.delete();

    console.log('✅ Credenciais do Flow removidas com sucesso!');
    console.log('🔄 Recarregue a página para ver as mudanças.');
    
    // Opcional: recarregar a página automaticamente
    if (confirm('Credenciais removidas! Deseja recarregar a página?')) {
      window.location.reload();
    }

  } catch (error) {
    console.error('❌ Erro ao limpar credenciais:', error);
  }
}

// Executar a função
clearFlowCredentials();
