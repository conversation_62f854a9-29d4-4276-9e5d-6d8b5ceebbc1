{"firestore": {"database": "(default)", "location": "us-east1", "rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "hosting": {"public": "dist", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**", "headers": [{"key": "Cross-Origin-Opener-Policy", "value": "same-origin-allow-popups"}, {"key": "Cross-Origin-Embedder-Policy", "value": "unsafe-none"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}]}}