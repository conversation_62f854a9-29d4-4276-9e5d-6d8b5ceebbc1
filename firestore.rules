rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Configurações do Flow por usuário
    match /flowConfigs/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Preferências do usuário (tema, persona cognitiva, etc.)
    match /userPreferences/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Sessões do Flow por usuário
    match /flowSessions/{sessionId} {
      // Permitir criação se o usuário está autenticado e o userId no documento é o mesmo do usuário
      allow create: if request.auth != null &&
        request.auth.uid == request.resource.data.userId;

      // Permitir leitura, atualização e exclusão se o usuário é o dono da sessão
      allow read, update, delete: if request.auth != null &&
        request.auth.uid == resource.data.userId;

      // Permitir listagem de sessões do usuário
      allow list: if request.auth != null &&
        request.auth.uid == resource.data.userId;
    }
  }
}