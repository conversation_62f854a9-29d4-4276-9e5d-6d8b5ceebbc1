// Service Worker para interceptar requisições e resolver CORS
const CACHE_NAME = 'flow-adapted-v2';
const FLOW_API_BASE = 'https://flow.ciandt.com';

console.log('🚀 [SW] Service Worker carregado');

// Instalar Service Worker
self.addEventListener('install', (event) => {
  console.log('🔧 [SW] Service Worker instalando...');
  // Força a ativação imediata
  self.skipWaiting();
});

// Ativar Service Worker
self.addEventListener('activate', (event) => {
  console.log('✅ [SW] Service Worker ativando...');
  event.waitUntil(
    Promise.all([
      // Limpar caches antigos
      caches.keys().then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== CACHE_NAME) {
              console.log('🗑️ [SW] Removendo cache antigo:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      // Tomar controle de todas as abas
      self.clients.claim()
    ])
  );
  console.log('✅ [SW] Service Worker ativo e controlando todas as abas');
});

// Interceptar requisições
self.addEventListener('fetch', (event) => {
  const url = new URL(event.request.url);

  console.log('🔍 [SW] Verificando requisição:', {
    url: event.request.url,
    hostname: url.hostname,
    method: event.request.method
  });

  // Interceptar requisições para a API Flow
  if (url.hostname === 'flow.ciandt.com') {
    console.log('🎯 [SW] INTERCEPTANDO requisição para Flow API:', {
      pathname: url.pathname,
      method: event.request.method,
      headers: Object.fromEntries(event.request.headers.entries())
    });

    event.respondWith(
      handleFlowRequest(event.request)
    );
  }
});

// Manipular requisições para a API Flow
async function handleFlowRequest(request) {
  console.log('🔄 [SW] Processando requisição Flow:', {
    url: request.url,
    method: request.method,
    headers: Object.fromEntries(request.headers.entries())
  });

  try {
    // Tratar requisições OPTIONS (preflight)
    if (request.method === 'OPTIONS') {
      console.log('✋ [SW] Respondendo a preflight request');
      return new Response(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization, FlowTenant, FlowAgent, Accept',
          'Access-Control-Max-Age': '86400'
        }
      });
    }

    // Clonar o corpo da requisição se existir
    let body = null;
    if (request.body && request.method !== 'GET') {
      body = await request.clone().text();
    }

    // Criar nova requisição com headers modificados
    const headers = new Headers();

    // Copiar headers originais
    for (const [key, value] of request.headers.entries()) {
      headers.set(key, value);
    }

    // Adicionar/modificar headers necessários
    headers.set('Origin', self.location.origin);
    headers.set('Referer', self.location.origin);

    console.log('📤 [SW] Enviando requisição modificada:', {
      url: request.url,
      method: request.method,
      headers: Object.fromEntries(headers.entries()),
      bodyLength: body ? body.length : 0
    });

    // Fazer a requisição
    const response = await fetch(request.url, {
      method: request.method,
      headers: headers,
      body: body,
      mode: 'cors',
      credentials: 'omit',
      cache: 'no-cache'
    });

    console.log('📥 [SW] Resposta recebida:', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok,
      headers: Object.fromEntries(response.headers.entries())
    });

    // Ler o corpo da resposta
    const responseBody = await response.text();

    // Criar nova resposta com headers CORS apropriados
    const corsHeaders = new Headers(response.headers);
    corsHeaders.set('Access-Control-Allow-Origin', '*');
    corsHeaders.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    corsHeaders.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, FlowTenant, FlowAgent, Accept');
    corsHeaders.set('Access-Control-Expose-Headers', '*');

    const modifiedResponse = new Response(responseBody, {
      status: response.status,
      statusText: response.statusText,
      headers: corsHeaders
    });

    console.log('✅ [SW] Resposta modificada enviada com headers CORS');
    return modifiedResponse;

  } catch (error) {
    console.error('❌ [SW] Erro ao processar requisição:', error);

    // Retornar erro em formato JSON com headers CORS
    return new Response(JSON.stringify({
      error: 'Erro no Service Worker',
      details: error.message,
      stack: error.stack
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, FlowTenant, FlowAgent'
      }
    });
  }
}
