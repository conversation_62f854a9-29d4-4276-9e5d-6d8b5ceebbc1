#!/bin/bash

# 🚀 Script de Deploy Automatizado - Adaptive Learning Platform
# Autor: Augment Agent
# Data: $(date)

set -e  # Parar em caso de erro

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠️  $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ❌ $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] ℹ️  $1${NC}"
}

# Banner
echo -e "${BLUE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    🚀 DEPLOY AUTOMÁTICO                     ║"
echo "║              Adaptive Learning Platform v1.0                ║"
echo "║                                                              ║"
echo "║  Preparando para deploy em produção...                      ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Verificar se estamos no diretório correto
if [ ! -f "package.json" ]; then
    error "Erro: package.json não encontrado. Execute este script na raiz do projeto."
    exit 1
fi

# Verificar se Firebase CLI está instalado
if ! command -v firebase &> /dev/null; then
    error "Firebase CLI não está instalado."
    info "Instale com: npm install -g firebase-tools"
    exit 1
fi

# Verificar se está logado no Firebase
if ! firebase projects:list &> /dev/null; then
    error "Você não está logado no Firebase."
    info "Execute: firebase login"
    exit 1
fi

# 1. Limpeza
log "🧹 Limpando arquivos antigos..."
rm -rf dist/
rm -rf node_modules/.vite/

# 2. Instalação de dependências
log "📦 Instalando dependências..."
npm ci --production=false

# 3. Lint e validação
log "🔍 Executando lint..."
npm run lint

# 4. Testes
log "🧪 Executando testes..."
npm run test

# 5. Build para produção
log "🏗️  Fazendo build para produção..."
NODE_ENV=production npm run build

# 6. Verificar se o build foi bem-sucedido
if [ ! -d "dist" ]; then
    error "Build falhou - diretório dist não foi criado"
    exit 1
fi

# 7. Verificar tamanho dos arquivos
log "📊 Verificando tamanho dos arquivos..."
du -sh dist/
find dist/ -name "*.js" -exec ls -lh {} \; | awk '{print $5 " " $9}' | sort -hr | head -5

# 8. Deploy para Firebase (apenas hosting)
log "🚀 Fazendo deploy para Firebase..."
firebase deploy --only hosting

# 9. Verificar se o deploy foi bem-sucedido
if [ $? -eq 0 ]; then
    log "✅ Deploy realizado com sucesso!"
    info "🌐 Aplicação disponível em: https://flow-adapted.web.app"
    info "🔗 Console Firebase: https://console.firebase.google.com/project/flow-adapted"
    info "🔧 Service Worker ativo para resolver CORS automaticamente"
    
    # Mostrar informações do deploy
    echo -e "${GREEN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    ✅ DEPLOY CONCLUÍDO                      ║"
    echo "║                                                              ║"
    echo "║  URL: https://flow-adapted.web.app                          ║"
    echo "║  Versão: 1.0.0                                              ║"
    echo "║  Data: $(date)                                    ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
else
    error "❌ Deploy falhou!"
    exit 1
fi
