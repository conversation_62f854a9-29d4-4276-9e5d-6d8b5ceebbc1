#!/bin/bash

# 🔍 Verificação Pré-Deploy
# Verifica se tudo está pronto para deploy

set -e

# Cores
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔍 VERIFICAÇÃO PRÉ-DEPLOY${NC}"
echo "=================================="

# 1. Verificar Node.js
echo -n "Node.js: "
if command -v node &> /dev/null; then
    echo -e "${GREEN}✅ $(node --version)${NC}"
else
    echo -e "${RED}❌ Não instalado${NC}"
    exit 1
fi

# 2. Verificar npm
echo -n "npm: "
if command -v npm &> /dev/null; then
    echo -e "${GREEN}✅ $(npm --version)${NC}"
else
    echo -e "${RED}❌ Não instalado${NC}"
    exit 1
fi

# 3. Verificar Firebase CLI
echo -n "Firebase CLI: "
if command -v firebase &> /dev/null; then
    echo -e "${GREEN}✅ $(firebase --version)${NC}"
else
    echo -e "${RED}❌ Não instalado${NC}"
    echo -e "${YELLOW}Execute: npm install -g firebase-tools${NC}"
    exit 1
fi

# 4. Verificar login Firebase
echo -n "Firebase Login: "
if firebase projects:list &> /dev/null; then
    echo -e "${GREEN}✅ Logado${NC}"
else
    echo -e "${RED}❌ Não logado${NC}"
    echo -e "${YELLOW}Execute: firebase login${NC}"
    exit 1
fi

# 5. Verificar projeto Firebase
echo -n "Projeto flow-adapted: "
if firebase projects:list | grep -q "flow-adapted"; then
    echo -e "${GREEN}✅ Encontrado${NC}"
else
    echo -e "${RED}❌ Não encontrado${NC}"
    echo -e "${YELLOW}Verifique se tem acesso ao projeto${NC}"
    exit 1
fi

# 6. Verificar package.json
echo -n "package.json: "
if [ -f "package.json" ]; then
    echo -e "${GREEN}✅ Encontrado${NC}"
else
    echo -e "${RED}❌ Não encontrado${NC}"
    exit 1
fi

# 7. Verificar firebase.json
echo -n "firebase.json: "
if [ -f "firebase.json" ]; then
    echo -e "${GREEN}✅ Encontrado${NC}"
else
    echo -e "${RED}❌ Não encontrado${NC}"
    exit 1
fi

# 8. Verificar node_modules
echo -n "Dependências: "
if [ -d "node_modules" ]; then
    echo -e "${GREEN}✅ Instaladas${NC}"
else
    echo -e "${YELLOW}⚠️  Não instaladas${NC}"
    echo "Instalando..."
    npm ci
fi

# 9. Teste de build
echo -n "Build test: "
if npm run build &> /dev/null; then
    echo -e "${GREEN}✅ Sucesso${NC}"
else
    echo -e "${RED}❌ Falhou${NC}"
    echo "Execute: npm run build"
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 TUDO PRONTO PARA DEPLOY!${NC}"
echo ""
echo -e "${BLUE}Execute agora:${NC}"
echo -e "${YELLOW}npm run deploy:quick${NC}"
echo ""
