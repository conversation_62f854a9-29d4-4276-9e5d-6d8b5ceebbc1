#!/bin/bash

# 🚀 Deploy Rápido - Adaptive Learning Platform
# Para quando você precisa fazer deploy AGORA!

set -e

# Cores
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 DEPLOY RÁPIDO INICIADO...${NC}"

# Build rápido
echo -e "${GREEN}📦 Build...${NC}"
npm run build

# Deploy direto
echo -e "${GREEN}🚀 Deploy...${NC}"
firebase deploy --only hosting

echo -e "${GREEN}✅ DEPLOY CONCLUÍDO!${NC}"
echo -e "${BLUE}🌐 https://flow-adapted.web.app${NC}"
