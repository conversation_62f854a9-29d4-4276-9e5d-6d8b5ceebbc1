import React from 'react';
import { AuthProvider } from '@/components/AuthProvider';
import { FlowProvider } from '@/components/FlowProvider';
import { ThemeProvider } from '@/contexts/ThemeContext';
import { AppRouter } from '@/app/router';

// Componente principal da aplicação que fornece contextos de autenticação e Flow
const App: React.FC = () => {
  return (
    <AuthProvider>
      <ThemeProvider>
        <FlowProvider>
          <AppRouter />
        </FlowProvider>
      </ThemeProvider>
    </AuthProvider>
  );
};

export default App;
