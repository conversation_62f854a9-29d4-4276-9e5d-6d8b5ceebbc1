import React, { useState, useEffect } from 'react';
import { useFlow } from './FlowProvider';
import { useAuth } from './AuthProvider';
import { FlowConfig } from '@/services/flowService';
import { Button } from '@/components/ui/Button';
import { Alert } from '@/components/ui/Alert';
import { Card } from '@/components/ui/Card';
import { flowStorageService } from '@/services/flow/flowStorageAdapter';

interface FlowConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export const FlowConfigModal: React.FC<FlowConfigModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const { saveConfig, clearConfig, loading, error } = useFlow();
  const { user } = useAuth();
  const [formData, setFormData] = useState<FlowConfig>({
    flowBaseUrl: 'https://flow.ciandt.com',
    flowTenant: '',
    flowClientId: '',
    flowClientSecret: '',
    flowAppToAccess: 'llm-api',
    flowAgent: 'chat',
    modelTemperature: 0.7
  });
  const [success, setSuccess] = useState<boolean>(false);
  const [validating, setValidating] = useState<boolean>(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [validationSuccess, setValidationSuccess] = useState<string | null>(null);
  const [showAdvanced, setShowAdvanced] = useState<boolean>(false);
  const [testing, setTesting] = useState<boolean>(false);
  const [hideFlowError, setHideFlowError] = useState<boolean>(false);
  const [loadingConfig, setLoadingConfig] = useState<boolean>(false);

  // Carregar credenciais salvas quando o modal abrir
  useEffect(() => {
    const loadSavedConfig = async () => {
      if (isOpen && user) {
        try {
          setLoadingConfig(true);
          const savedConfig = await flowStorageService.getFlowConfig(user.uid);

          if (savedConfig) {
            console.log("📋 [FlowConfigModal] Carregando credenciais salvas");
            setFormData({
              flowBaseUrl: savedConfig.flowBaseUrl || 'https://flow.ciandt.com',
              flowTenant: savedConfig.flowTenant || '',
              flowClientId: savedConfig.flowClientId || '',
              flowClientSecret: savedConfig.flowClientSecret || '',
              flowAppToAccess: savedConfig.flowAppToAccess || 'llm-api',
              flowAgent: savedConfig.flowAgent || 'chat',
              modelTemperature: savedConfig.modelTemperature || 0.7
            });
          } else {
            console.log("📋 [FlowConfigModal] Nenhuma credencial salva encontrada");
          }
        } catch (err) {
          console.error("❌ [FlowConfigModal] Erro ao carregar credenciais:", err);
        } finally {
          setLoadingConfig(false);
        }
      }
    };

    loadSavedConfig();
  }, [isOpen, user]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'modelTemperature' ? parseFloat(value) : value
    }));
    setSuccess(false);
    setValidationSuccess(null);
    setValidationError(null);
    setHideFlowError(false);
  };

  const validateCredentials = async (): Promise<boolean> => {
    setValidating(true);
    setValidationError(null);
    setValidationSuccess(null);

    try {
      const { FlowService } = await import('../services/flowService');
      const tempService = new FlowService(formData);
      await tempService.listModels();

      setValidating(false);
      return true;
    } catch (err) {
      console.error("Erro ao validar credenciais:", err);
      setValidationError(err instanceof Error ? err.message : 'Falha ao validar as credenciais do Flow');
      setValidating(false);
      return false;
    }
  };

  const handleTestConnection = async () => {
    setTesting(true);
    setValidationError(null);
    setValidationSuccess(null);

    try {
      const { FlowService } = await import('../services/flowService');
      const tempService = new FlowService(formData);
      const models = await tempService.listModels();

      setValidationSuccess(`Conexão bem-sucedida! Encontrados ${models.length} modelos disponíveis.`);
      setTesting(false);
    } catch (err) {
      console.error("Erro ao testar conexão:", err);
      setValidationError(err instanceof Error ? err.message : 'Falha ao conectar com o Flow');
      setTesting(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const isValid = await validateCredentials();
      if (!isValid) {
        return;
      }
      
      await saveConfig(formData);
      setSuccess(true);
      
      // Aguarda um pouco para mostrar o sucesso, depois chama callbacks
      setTimeout(() => {
        onSuccess?.();
        onClose();
      }, 1500);
    } catch (err) {
      console.error("Erro ao salvar configuração:", err);
    }
  };

  const handleClearConfig = async () => {
    if (!confirm('Tem certeza que deseja limpar todas as credenciais salvas? Esta ação não pode ser desfeita.')) {
      return;
    }

    try {
      await clearConfig();
      setValidationSuccess('Credenciais removidas com sucesso!');

      // Resetar formulário para valores padrão (campos vazios)
      setFormData({
        flowBaseUrl: 'https://flow.ciandt.com',
        flowTenant: '',
        flowClientId: '',
        flowClientSecret: '',
        flowAppToAccess: 'llm-api',
        flowAgent: 'chat',
        modelTemperature: 0.7
      });

      setTimeout(() => {
        onClose();
      }, 1500);
    } catch (err) {
      console.error("Erro ao limpar configuração:", err);
      setValidationError(err instanceof Error ? err.message : 'Erro ao limpar configuração');
    }
  };

  const handleClose = () => {
    if (!loading && !validating && !testing) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Overlay */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={handleClose}
      />
      
      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-2">
        <Card className="relative w-full max-w-xl card-theme shadow-xl">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-theme">
            <h2 className="text-lg font-semibold text-theme-primary">
              🔧 Configuração do Flow
            </h2>
            <button
              onClick={handleClose}
              disabled={loading || validating || testing}
              className="text-theme-secondary hover:text-theme-primary transition-colors disabled:opacity-50"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Content */}
          <div className="p-4">
            {loadingConfig && (
              <div className="mb-3 flex items-center justify-center py-4">
                <div className="flex items-center space-x-2 text-theme-secondary">
                  <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span className="text-xs">Carregando credenciais salvas...</span>
                </div>
              </div>
            )}

            <div className="mb-3">
              <p className="text-theme-secondary text-xs">
                Configure suas credenciais pessoais do Flow. Cada usuário deve informar seu próprio <strong>Tenant</strong>, <strong>Client ID</strong> e <strong>Client Secret</strong>.
              </p>
              <div className="mt-2 p-2 bg-theme-secondary/30 rounded border border-theme-accent/30">
                <p className="text-theme-secondary text-xs">
                  📋 <strong>Onde obter suas credenciais:</strong>
                </p>
                <a
                  href="https://flow.ciandt.com/settings/api-keys"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-theme-accent hover:text-theme-accent-hover underline text-xs font-medium inline-flex items-center mt-1"
                >
                  🔗 flow.ciandt.com/settings/api-keys
                  <svg className="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
              </div>
            </div>

            {success && (
              <div className="mb-3">
                <div className="relative">
                  <Alert variant="success" title="Sucesso!">
                    Configuração salva com sucesso!
                  </Alert>
                  <button
                    onClick={() => setSuccess(false)}
                    className="absolute top-2 right-2 text-green-600 hover:text-green-800 transition-colors"
                    aria-label="Fechar"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>
            )}

            {error && !hideFlowError && (
              <div className="mb-3">
                <div className="relative">
                  <Alert variant="error" title="Erro">
                    {error}
                  </Alert>
                  <button
                    onClick={() => setHideFlowError(true)}
                    className="absolute top-2 right-2 text-red-600 hover:text-red-800 transition-colors"
                    aria-label="Fechar"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>
            )}

            {validationError && (
              <div className="mb-3">
                <div className="relative">
                  <Alert variant="warning" title="Erro de Conexão">
                    {validationError}
                  </Alert>
                  <button
                    onClick={() => setValidationError(null)}
                    className="absolute top-2 right-2 text-orange-600 hover:text-orange-800 transition-colors"
                    aria-label="Fechar"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>
            )}

            {validationSuccess && (
              <div className="mb-3">
                <div className="relative">
                  <Alert variant="success" title="Teste de Conexão">
                    {validationSuccess}
                  </Alert>
                  <button
                    onClick={() => setValidationSuccess(null)}
                    className="absolute top-2 right-2 text-green-600 hover:text-green-800 transition-colors"
                    aria-label="Fechar"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>
            )}
            
            <form onSubmit={handleSubmit} className="space-y-3">
              {/* Campos básicos */}
              <div>
                <label htmlFor="flowTenant" className="block text-xs font-medium text-theme-primary mb-1">
                  Flow Tenant *
                </label>
                <input
                  type="text"
                  id="flowTenant"
                  name="flowTenant"
                  value={formData.flowTenant}
                  onChange={handleChange}
                  className="w-full px-2 py-1.5 text-sm border border-theme rounded bg-theme-primary text-theme-primary focus:outline-none focus:ring-1 focus:ring-theme-focus focus:border-theme-focus"
                  required
                  placeholder="Seu tenant do Flow (ex: cit)"
                />
              </div>
              
              <div>
                <label htmlFor="flowClientId" className="block text-xs font-medium text-theme-primary mb-1">
                  ID do Cliente *
                </label>
                <input
                  type="text"
                  id="flowClientId"
                  name="flowClientId"
                  value={formData.flowClientId}
                  onChange={handleChange}
                  className="w-full px-2 py-1.5 text-sm border border-theme rounded bg-theme-primary text-theme-primary focus:outline-none focus:ring-1 focus:ring-theme-focus focus:border-theme-focus"
                  required
                  placeholder="Seu Client ID pessoal do Flow"
                />
              </div>
              
              <div>
                <label htmlFor="flowClientSecret" className="block text-xs font-medium text-theme-primary mb-1">
                  Chave Secreta *
                </label>
                <input
                  type="password"
                  id="flowClientSecret"
                  name="flowClientSecret"
                  value={formData.flowClientSecret}
                  onChange={handleChange}
                  className="w-full px-2 py-1.5 text-sm border border-theme rounded bg-theme-primary text-theme-primary focus:outline-none focus:ring-1 focus:ring-theme-focus focus:border-theme-focus"
                  required
                  placeholder="Sua chave secreta pessoal do Flow"
                />
              </div>
              
              {/* Configurações avançadas */}
              <div className="border-t border-theme pt-3">
                <button
                  type="button"
                  onClick={() => setShowAdvanced(!showAdvanced)}
                  className="flex items-center justify-between w-full text-left text-xs font-medium text-theme-secondary hover:text-theme-primary focus:outline-none"
                >
                  <span>⚙️ Configurações Avançadas</span>
                  <svg
                    className={`w-3 h-3 transform transition-transform ${showAdvanced ? 'rotate-180' : ''}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                
                {showAdvanced && (
                  <div className="mt-3 space-y-3 pl-3 border-l-2 border-theme-accent">
                    <div>
                      <label htmlFor="flowBaseUrl" className="block text-xs font-medium text-theme-primary mb-1">
                        URL Base
                      </label>
                      <input
                        type="text"
                        id="flowBaseUrl"
                        name="flowBaseUrl"
                        value={formData.flowBaseUrl}
                        onChange={handleChange}
                        className="w-full px-2 py-1.5 text-sm border border-theme rounded bg-theme-primary text-theme-primary focus:outline-none focus:ring-1 focus:ring-theme-focus focus:border-theme-focus"
                        required
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="flowAppToAccess" className="block text-xs font-medium text-theme-primary mb-1">
                        Aplicação de Acesso
                      </label>
                      <input
                        type="text"
                        id="flowAppToAccess"
                        name="flowAppToAccess"
                        value={formData.flowAppToAccess}
                        onChange={handleChange}
                        className="w-full px-2 py-1.5 text-sm border border-theme rounded bg-theme-primary text-theme-primary focus:outline-none focus:ring-1 focus:ring-theme-focus focus:border-theme-focus"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="modelTemperature" className="block text-xs font-medium text-theme-primary mb-1">
                        Temperatura: {formData.modelTemperature.toFixed(1)}
                      </label>
                      <input
                        type="range"
                        id="modelTemperature"
                        name="modelTemperature"
                        min="0"
                        max="1"
                        step="0.1"
                        value={formData.modelTemperature}
                        onChange={handleChange}
                        className="w-full h-1.5 bg-theme-secondary rounded-lg appearance-none cursor-pointer"
                      />
                      <div className="flex justify-between text-xs text-theme-muted mt-1">
                        <span>Previsível</span>
                        <span>Criativo</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </form>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between p-4 border-t border-theme">
            {/* Botão de limpar à esquerda */}
            <Button
              variant="error"
              onClick={handleClearConfig}
              disabled={loading || validating || testing}
              className="text-sm py-1.5 px-3"
            >
              🗑️ Limpar
            </Button>

            {/* Botões principais à direita */}
            <div className="flex items-center space-x-2">
              <Button
                variant="secondary"
                onClick={handleClose}
                disabled={loading || validating || testing}
                className="text-sm py-1.5 px-3"
              >
                Cancelar
              </Button>
              <Button
                variant="outline"
                onClick={handleTestConnection}
                isLoading={testing}
                disabled={loading || validating || testing || !formData.flowTenant || !formData.flowClientId || !formData.flowClientSecret}
                className="text-sm py-1.5 px-3"
              >
                {testing ? 'Testando...' : '🔗 Testar'}
              </Button>
              <Button
                variant="primary"
                onClick={handleSubmit}
                isLoading={loading || validating}
                disabled={loading || validating || testing}
                className="text-sm py-1.5 px-3"
              >
                {validating ? 'Validando...' : loading ? 'Salvando...' : 'Salvar'}
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};
