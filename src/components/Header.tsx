import React from 'react';
import { useAuth } from '@/components/AuthProvider';
import { ThemeSelector } from '@/components/ThemeSelector';
import { LoggedHeader } from '@/components/LoggedHeader';

interface HeaderProps {
  title?: string;
  forceSimple?: boolean; // Para forçar o header simples mesmo logado
}

export const Header: React.FC<HeaderProps> = ({ title = "Flow AdaptEd", forceSimple = false }) => {
  const { user } = useAuth();

  // Se o usuário está logado e não forçamos o header simples, usa o LoggedHeader
  if (user && !forceSimple) {
    return <LoggedHeader />;
  }

  // Header simples para páginas não logadas
  return (
    <header className="bg-theme-primary text-white shadow-lg sticky top-0 z-40">
      <div className="container mx-auto px-3 sm:px-4 py-3 flex justify-between items-center">
        <div className="flex items-center">
          <div
            className="w-8 h-8 sm:w-9 sm:h-9 mr-2.5 rounded-lg flex items-center justify-center shadow-sm"
            style={{
              background: `linear-gradient(135deg, rgb(var(--color-primary)), rgb(var(--color-accent)))`
            }}
          >
            <span className="text-white font-bold text-sm sm:text-base">🧠</span>
          </div>
          <h1 className="text-xl sm:text-2xl font-bold tracking-tight">{title}</h1>
        </div>

        <div className="flex items-center">
          <ThemeSelector />
        </div>
      </div>
    </header>
  );
};