import React, { useState } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { COGNITIVE_PROFILES } from '@/constants';
import { Button } from '@/components/ui/Button';

interface PersonaPreviewProps {
  compact?: boolean;
}

export const PersonaPreview: React.FC<PersonaPreviewProps> = ({ compact = false }) => {
  const { persona, setPersona, theme, effectiveTheme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);

  if (compact) {
    const currentProfile = COGNITIVE_PROFILES.find(p => p.id === persona);

    return (
      <div className="relative">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="header-button"
          aria-label="Seletor de persona"
        >
          <div className="flex items-center space-x-2">
            <span className="text-lg">{currentProfile?.icon || '🧠'}</span>
            <span className="text-sm font-medium text-theme-primary hidden sm:block">
              {currentProfile?.name || 'Padrão'}
            </span>
            <svg
              className={`w-3 h-3 text-theme-secondary transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </button>

        {isOpen && (
          <>
            <div
              className="fixed inset-0 z-40"
              onClick={() => setIsOpen(false)}
            />

            <div className="absolute top-full right-0 mt-2 w-64 bg-theme-primary border border-theme rounded-lg shadow-xl z-50 overflow-hidden">
              <div className="p-3">
                <div className="text-xs font-semibold text-theme-secondary mb-3">
                  Perfil Cognitivo
                </div>

                <div className="grid grid-cols-2 gap-2">
                  {COGNITIVE_PROFILES.map((profile) => (
                    <button
                      key={profile.id}
                      onClick={() => {
                        setPersona(profile.id);
                        setIsOpen(false);
                      }}
                      className={`flex flex-col items-center p-2 rounded-md text-center transition-colors duration-150 ${
                        persona === profile.id
                          ? 'bg-theme-tertiary text-theme-primary'
                          : 'hover:bg-theme-secondary text-theme-secondary hover:text-theme-primary'
                      }`}
                    >
                      <span className="text-lg mb-1">{profile.icon}</span>
                      <span className="text-xs font-medium">{profile.name}</span>
                    </button>
                  ))}
                </div>
              </div>

              <div className="border-t border-theme px-3 py-2 bg-theme-secondary">
                <div className="text-xs text-theme-muted">
                  <span className="font-medium">Ativo:</span> {currentProfile?.name || 'Padrão'}
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    );
  }

  return (
    <div className="bg-theme-secondary p-6 rounded-lg border border-theme">
      <h3 className="font-semibold text-theme-primary mb-4 text-center">
        🧠 Experimente diferentes perfis cognitivos
      </h3>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
        {COGNITIVE_PROFILES.map((profile) => (
          <Button
            key={profile.id}
            variant={persona === profile.id ? "primary" : "secondary"}
            onClick={() => setPersona(profile.id)}
            className="flex flex-col items-center p-3 h-auto text-center"
          >
            <span className="text-2xl mb-1">{profile.icon}</span>
            <span className="text-xs font-medium">{profile.name}</span>
          </Button>
        ))}
      </div>

      <div className="mt-4 p-3 bg-theme-tertiary rounded border-l-4 border-theme-focus">
        {(() => {
          const currentProfile = COGNITIVE_PROFILES.find(p => p.id === persona);
          return (
            <div>
              <h4 className="font-medium text-theme-primary text-sm">
                {currentProfile?.icon} {currentProfile?.name}
              </h4>
              <p className="text-xs text-theme-secondary mt-1">
                {currentProfile?.description}
              </p>
            </div>
          );
        })()}
      </div>

      <div className="mt-3 text-center">
        <p className="text-xs text-theme-muted">
          Cada perfil adapta cores, fontes e layout para melhor experiência
        </p>
        <p className="text-xs text-theme-muted mt-1">
          Tema: <span className="font-medium">{effectiveTheme === 'dark' ? 'Escuro' : 'Claro'}</span>
          {theme === 'system' && <span className="ml-1">(automático)</span>}
        </p>
      </div>
    </div>
  );
};
