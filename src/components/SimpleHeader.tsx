import React from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { ThemeToggle } from '@/components/ThemeToggle';
import { PersonaPreview } from '@/components/PersonaPreview';
import { COGNITIVE_PROFILES } from '@/constants';

interface SimpleHeaderProps {
  showPersonaInfo?: boolean;
  showControls?: boolean;
  className?: string;
}

export const SimpleHeader: React.FC<SimpleHeaderProps> = ({ 
  showPersonaInfo = true, 
  showControls = true,
  className = ''
}) => {
  const { persona } = useTheme();
  
  // Buscar o nome traduzido da persona
  const currentProfile = COGNITIVE_PROFILES.find(p => p.id === persona);
  const personaName = currentProfile?.name ?? 'Padrão';

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 bg-theme-secondary/95 backdrop-blur-sm border-b border-theme shadow-sm ${className}`}>
      <div className="max-w-7xl mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Logo e título */}
          <div className="flex items-center space-x-3">
            <div
              className="w-8 h-8 rounded-lg flex items-center justify-center shadow-sm"
              style={{
                background: `linear-gradient(135deg, rgb(var(--color-primary)), rgb(var(--color-accent)))`
              }}
            >
              <span className="text-white font-bold text-sm">🧠</span>
            </div>
            <div className="flex flex-col">
              <h1 className="text-lg font-bold text-theme-primary leading-tight">
                Flow AdaptEd
              </h1>
              {showPersonaInfo && (
                <div className="flex items-center space-x-1 text-xs text-theme-secondary">
                  <span>Perfil:</span>
                  <span className="font-medium text-theme-primary">{personaName}</span>
                </div>
              )}
            </div>
          </div>

          {/* Controles do lado direito */}
          {showControls && (
            <div className="flex items-center space-x-2">
              <PersonaPreview compact />
              <ThemeToggle />
            </div>
          )}
        </div>
      </div>
    </header>
  );
};
