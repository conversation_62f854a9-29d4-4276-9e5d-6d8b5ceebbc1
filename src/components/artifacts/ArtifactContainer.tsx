/**
 * Container principal para exibição de artefatos
 */

import React from 'react';
import { ChatMessageArtifact } from '@/types/chat';
import { ArtifactLayoutConfig } from '@/types/artifacts';
import { ArtifactToolbar } from './ArtifactToolbar';
import { ArtifactRenderer } from './ArtifactRenderer';

interface ArtifactContainerProps {
  artifact: ChatMessageArtifact;
  layoutConfig: ArtifactLayoutConfig;
  isEditing?: boolean;
  onAction: (action: string, data?: any) => void;
  className?: string;
}

export const ArtifactContainer: React.FC<ArtifactContainerProps> = ({
  artifact,
  layoutConfig,
  isEditing = false,
  onAction,
  className = '',
}) => {
  const { isFullscreen, artifactPosition } = layoutConfig;

  // Classes CSS baseadas no layout
  const containerClasses = [
    'artifact-container',
    'bg-theme-primary',
    'border',
    'border-theme',
    'rounded-lg',
    'shadow-lg',
    'flex',
    'flex-col',
    'overflow-hidden',
    className,
    // Layout específico
    isFullscreen ? 'fixed inset-4 z-50' : '',
    artifactPosition === 'bottom' ? 'w-full' : 'h-full',
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses}>
      {/* Header com título e toolbar */}
      <div className="artifact-header bg-theme-secondary border-b border-theme px-4 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-3 min-w-0 flex-1">
          {/* Ícone do tipo */}
          <div className="artifact-icon flex-shrink-0">
            {getArtifactIcon(artifact.type)}
          </div>
          
          {/* Título e metadados */}
          <div className="min-w-0 flex-1">
            <h3 className="text-sm font-medium text-theme-primary truncate">
              {artifact.title || 'Artefato'}
            </h3>
            <div className="flex items-center space-x-2 text-xs text-theme-muted">
              <span>{artifact.type}</span>
              {artifact.language && (
                <>
                  <span>•</span>
                  <span>{artifact.language}</span>
                </>
              )}
              <span>•</span>
              <span>{formatDate(artifact.updatedAt)}</span>
            </div>
          </div>
        </div>

        {/* Toolbar */}
        <ArtifactToolbar
          artifact={artifact}
          isEditing={isEditing}
          isFullscreen={isFullscreen}
          onAction={onAction}
        />
      </div>

      {/* Conteúdo do artefato */}
      <div className="artifact-content flex-1 overflow-hidden">
        <ArtifactRenderer
          artifact={artifact}
          isEditing={isEditing}
          onContentChange={(content) => onAction('update-content', { content })}
        />
      </div>

      {/* Footer com informações adicionais (se necessário) */}
      {artifact.versions && artifact.versions.length > 0 && (
        <div className="artifact-footer bg-theme-secondary border-t border-theme px-4 py-2">
          <div className="flex items-center justify-between text-xs text-theme-muted">
            <span>{artifact.versions.length} versão(ões)</span>
            <button
              onClick={() => onAction('show-versions')}
              className="text-theme-accent hover:text-theme-focus transition-colors"
            >
              Ver histórico
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

// Ícones para diferentes tipos de artefatos
function getArtifactIcon(type: ChatMessageArtifact['type']) {
  const iconClasses = "w-4 h-4 text-theme-accent";
  
  switch (type) {
    case 'code':
      return (
        <svg className={iconClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
        </svg>
      );
    
    case 'react':
      return (
        <svg className={iconClasses} fill="currentColor" viewBox="0 0 24 24">
          <path d="M14.23 12.004a2.236 2.236 0 0 1-2.235 2.236 2.236 2.236 0 0 1-2.236-2.236 2.236 2.236 0 0 1 2.235-2.236 2.236 2.236 0 0 1 2.236 2.236zm2.648-10.69c-1.346 0-3.107.96-4.888 2.622-1.78-1.653-3.542-2.602-4.887-2.602-.41 0-.783.093-1.106.278-1.375.793-1.683 3.264-.973 6.365C1.98 8.917 0 10.42 0 12.004c0 1.59 1.99 3.097 5.043 4.03-.704 3.113-.39 5.588.988 ***********.69.275 1.102.275 1.345 0 3.107-.96 4.888-2.624 1.78 1.654 3.542 2.603 4.887 2.603.41 0 .783-.09 1.106-.275 1.374-.792 1.683-3.263.973-6.365C22.02 15.096 24 13.59 24 12.004c0-1.59-1.99-3.097-5.043-4.032.704-3.11.39-5.587-.988-6.38-.318-.184-.688-.277-1.092-.278zm-.005 1.09v.006c.225 0 .406.044.558.127.666.382.955 1.835.73 3.704-.054.46-.142.945-.25 1.44-.96-.236-2.006-.417-3.107-.534-.66-.905-1.345-1.727-2.035-2.447 1.592-1.48 3.087-2.292 4.105-2.295zm-9.77.02c1.012 0 2.514.808 4.11 2.28-.686.72-1.37 1.537-2.02 2.442-1.107.117-2.154.298-3.113.538-.112-.49-.195-.964-.254-1.42-.23-1.868.054-3.32.714-3.707.19-.09.4-.127.563-.132zm4.882 3.05c.455.468.91.992 1.36 1.564-.44-.02-.89-.034-1.36-.034-.47 0-.92.014-1.36.034.44-.572.895-1.096 1.36-1.564zM12 8.1c.74 0 1.477.034 2.202.093.406.582.802 1.203 1.183 1.86.372.64.71 1.29 1.018 1.946-.308.655-.646 1.31-1.013 1.95-.38.66-.773 1.288-1.18 1.87-.728.063-1.466.098-2.21.098-.74 0-1.477-.035-2.202-.093-.406-.582-.802-1.204-1.183-1.86-.372-.64-.71-1.29-1.018-1.946.303-.657.646-1.313 1.013-1.954.38-.66.773-1.286 1.18-1.868.728-.064 1.466-.098 2.21-.098zm-3.635.254c-.24.377-.48.763-.704 1.16-.225.39-.435.782-.635 1.174-.265-.656-.49-1.31-.676-1.947.64-.15 1.315-.283 2.015-.386zm7.26 0c.695.103 1.365.23 2.006.387-.18.632-.405 1.282-.66 1.933-.2-.39-.41-.783-.64-1.174-.225-.392-.465-.774-.705-1.146zm3.063.675c.484.15.944.317 1.375.498 1.732.74 2.852 1.708 2.852 2.476-.005.768-1.125 1.74-2.857 2.475-.42.18-.88.342-1.355.493-.28-.958-.646-1.956-1.1-2.98.45-1.017.81-2.01 1.085-2.964zm-13.395.004c.278.96.645 1.957 1.1 2.98-.45 1.017-.812 2.01-1.086 2.964-.484-.15-.944-.318-1.37-.5-1.732-.737-2.852-1.706-2.852-2.474 0-.768 1.12-1.742 2.852-2.476.42-.18.88-.342 1.356-.494zm11.678 4.28c.265.657.49 1.312.676 1.948-.64.157-1.316.29-2.016.39.24-.375.48-.762.705-1.158.225-.39.435-.788.636-1.18zm-9.945.02c.2.392.41.783.64 1.175.23.39.465.772.705 1.143-.695-.102-1.365-.23-2.006-.386.18-.63.406-1.282.66-1.933zM17.92 16.32c.112.493.2.968.254 1.423.23 1.868-.054 3.32-.714 3.708-.147.09-.338.128-.563.128-1.012 0-2.514-.807-4.11-2.28.686-.72 1.37-1.536 2.02-2.44 1.107-.118 2.154-.3 3.113-.54zm-11.83.01c.96.234 2.006.415 3.107.532.66.905 1.345 1.727 2.035 2.446-1.595 1.483-3.092 2.295-4.11 2.295-.22-.005-.406-.05-.553-.132-.666-.38-.955-1.834-.73-3.703.054-.46.142-.944.25-1.438zm4.56.64c.44.02.89.034 1.36.034.47 0 .92-.014 1.36-.034-.44.572-.895 1.095-1.36 1.56-.465-.467-.92-.992-1.36-1.56z"/>
        </svg>
      );
    
    case 'html':
      return (
        <svg className={iconClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
      );
    
    case 'document':
      return (
        <svg className={iconClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      );
    
    case 'svg':
      return (
        <svg className={iconClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      );
    
    default:
      return (
        <svg className={iconClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      );
  }
}

// Formatar data para exibição
function formatDate(date: Date): string {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const minutes = Math.floor(diff / 60000);
  
  if (minutes < 1) return 'agora';
  if (minutes < 60) return `${minutes}min atrás`;
  
  const hours = Math.floor(minutes / 60);
  if (hours < 24) return `${hours}h atrás`;
  
  const days = Math.floor(hours / 24);
  if (days < 7) return `${days}d atrás`;
  
  return date.toLocaleDateString('pt-BR');
}
