/**
 * Modal elegante e simples para exibir artefatos
 */

import React, { useState } from 'react';
import { ChatMessageArtifact } from '@/types/chat';

interface ArtifactModalProps {
  artifact: ChatMessageArtifact;
  onClose: () => void;
}

export const ArtifactModal: React.FC<ArtifactModalProps> = ({
  artifact,
  onClose,
}) => {
  const [viewMode, setViewMode] = useState<'preview' | 'code'>('preview');

  // Copiar conteúdo
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(artifact.content);
      // TODO: Adicionar toast de sucesso
    } catch (error) {
      console.error('Erro ao copiar:', error);
    }
  };

  // Baixar arquivo
  const handleDownload = () => {
    const blob = new Blob([artifact.content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    
    a.href = url;
    a.download = `${artifact.title || 'artifact'}.${getFileExtension(artifact)}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Obter extensão do arquivo
  const getFileExtension = (artifact: ChatMessageArtifact): string => {
    switch (artifact.type) {
      case 'code':
        switch (artifact.language) {
          case 'javascript': return 'js';
          case 'typescript': return 'ts';
          case 'python': return 'py';
          case 'html': return 'html';
          case 'css': return 'css';
          case 'json': return 'json';
          default: return 'txt';
        }
      case 'react': return 'tsx';
      case 'html': return 'html';
      case 'document': return 'md';
      case 'svg': return 'svg';
      default: return 'txt';
    }
  };

  // Renderizar preview baseado no tipo
  const renderPreview = () => {
    switch (artifact.type) {
      case 'html':
        return (
          <iframe
            srcDoc={artifact.content}
            className="w-full h-full border-0 bg-white rounded"
            title="Preview HTML"
            sandbox="allow-same-origin allow-scripts allow-forms"
          />
        );
      
      case 'svg':
        return (
          <div className="w-full h-full flex items-center justify-center bg-white rounded">
            <div dangerouslySetInnerHTML={{ __html: artifact.content }} />
          </div>
        );
      
      default:
        return (
          <div className="w-full h-full overflow-auto bg-gray-900 text-green-400 rounded p-4">
            <pre className="text-sm font-mono whitespace-pre-wrap">
              {artifact.content}
            </pre>
          </div>
        );
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-theme-primary rounded-lg shadow-2xl w-full max-w-6xl h-full max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-theme">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-theme-accent rounded-full flex items-center justify-center">
              {getArtifactIcon(artifact.type)}
            </div>
            <div>
              <h2 className="text-lg font-semibold text-theme-primary">
                {artifact.title || 'Artefato'}
              </h2>
              <p className="text-sm text-theme-muted">
                {artifact.type} • {artifact.content.split('\n').length} linhas
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {/* Toggle view mode para HTML/SVG */}
            {(artifact.type === 'html' || artifact.type === 'svg') && (
              <button
                onClick={() => setViewMode(viewMode === 'preview' ? 'code' : 'preview')}
                className="px-3 py-1 text-sm bg-theme-secondary text-theme-primary rounded hover:bg-theme-accent hover:text-white transition-colors"
              >
                {viewMode === 'preview' ? 'Ver código' : 'Ver preview'}
              </button>
            )}

            {/* Botão copiar */}
            <button
              onClick={handleCopy}
              className="p-2 text-theme-muted hover:text-theme-primary transition-colors"
              title="Copiar"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
            </button>

            {/* Botão baixar */}
            <button
              onClick={handleDownload}
              className="p-2 text-theme-muted hover:text-theme-primary transition-colors"
              title="Baixar"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </button>

            {/* Botão fechar */}
            <button
              onClick={onClose}
              className="p-2 text-theme-muted hover:text-red-500 transition-colors"
              title="Fechar"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Conteúdo */}
        <div className="flex-1 p-4 overflow-hidden">
          {viewMode === 'preview' && (artifact.type === 'html' || artifact.type === 'svg') ? (
            renderPreview()
          ) : (
            <div className="w-full h-full overflow-auto bg-gray-900 text-green-400 rounded p-4">
              <pre className="text-sm font-mono whitespace-pre-wrap">
                {artifact.content}
              </pre>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Ícone do artefato
const getArtifactIcon = (type: string) => {
  const iconClasses = "w-4 h-4 text-white";
  
  switch (type) {
    case 'code':
      return (
        <svg className={iconClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
        </svg>
      );
    case 'html':
      return (
        <svg className={iconClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
      );
    case 'document':
      return (
        <svg className={iconClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      );
    default:
      return (
        <svg className={iconClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      );
  }
};
