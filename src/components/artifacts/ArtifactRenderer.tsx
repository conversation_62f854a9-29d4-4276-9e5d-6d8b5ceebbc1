/**
 * Renderizador principal de artefatos - delega para componentes específicos
 */

import React, { Suspense } from 'react';
import { ChatMessageArtifact } from '@/types/chat';

// Lazy loading dos componentes específicos
const CodeArtifact = React.lazy(() => import('./CodeArtifact'));
const HtmlArtifact = React.lazy(() => import('./HtmlArtifact'));
const DocumentArtifact = React.lazy(() => import('./DocumentArtifact'));
const SvgArtifact = React.lazy(() => import('./SvgArtifact'));
const ReactArtifact = React.lazy(() => import('./ReactArtifact'));

interface ArtifactRendererProps {
  artifact: ChatMessageArtifact;
  isEditing: boolean;
  onContentChange?: (content: string) => void;
}

export const ArtifactRenderer: React.FC<ArtifactRendererProps> = ({
  artifact,
  isEditing,
  onContentChange,
}) => {
  // Loading fallback
  const LoadingFallback = () => (
    <div className="flex items-center justify-center h-32">
      <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-theme-accent"></div>
    </div>
  );

  // Error boundary fallback
  const ErrorFallback = ({ error }: { error: Error }) => (
    <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded">
      <div className="flex items-center space-x-2 text-red-800 dark:text-red-200">
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span className="font-medium">Erro ao renderizar artefato</span>
      </div>
      <p className="text-sm text-red-600 dark:text-red-300 mt-1">
        {error.message}
      </p>
    </div>
  );

  // Renderizar componente específico baseado no tipo
  const renderArtifactContent = () => {
    const commonProps = {
      artifact,
      isEditing,
      onContentChange,
    };

    switch (artifact.type) {
      case 'code':
        return <CodeArtifact {...commonProps} />;
      
      case 'react':
        return <ReactArtifact {...commonProps} />;
      
      case 'html':
        return <HtmlArtifact {...commonProps} />;
      
      case 'document':
        return <DocumentArtifact {...commonProps} />;
      
      case 'svg':
        return <SvgArtifact {...commonProps} />;
      
      case 'image':
        return (
          <div className="p-4 text-center">
            {artifact.dataUrl ? (
              <img 
                src={artifact.dataUrl} 
                alt={artifact.title || 'Imagem gerada'}
                className="max-w-full h-auto mx-auto rounded"
              />
            ) : (
              <div className="text-theme-muted">
                <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <p>Imagem não disponível</p>
              </div>
            )}
          </div>
        );
      
      default:
        return (
          <div className="p-4">
            <div className="text-center text-theme-muted">
              <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p className="font-medium">Tipo de artefato não suportado</p>
              <p className="text-sm">Tipo: {artifact.type}</p>
            </div>
            
            {/* Fallback: mostrar conteúdo como texto */}
            <div className="mt-4 p-3 bg-theme-secondary rounded border">
              <pre className="text-sm whitespace-pre-wrap text-theme-primary">
                {artifact.content}
              </pre>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="artifact-renderer h-full overflow-hidden">
      <ErrorBoundary fallback={ErrorFallback}>
        <Suspense fallback={<LoadingFallback />}>
          {renderArtifactContent()}
        </Suspense>
      </ErrorBoundary>
    </div>
  );
};

// Error Boundary simples
class ErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback: React.ComponentType<{ error: Error }> },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Erro no ArtifactRenderer:', error, errorInfo);
  }

  render() {
    if (this.state.hasError && this.state.error) {
      const Fallback = this.props.fallback;
      return <Fallback error={this.state.error} />;
    }

    return this.props.children;
  }
}
