/**
 * Toolbar com ações para artefatos
 */

import React, { useState } from 'react';
import { ChatMessageArtifact } from '@/types/chat';
import { Button } from '@/components/ui/Button';

interface ArtifactToolbarProps {
  artifact: ChatMessageArtifact;
  isEditing: boolean;
  isFullscreen: boolean;
  onAction: (action: string, data?: any) => void;
}

export const ArtifactToolbar: React.FC<ArtifactToolbarProps> = ({
  artifact,
  isEditing,
  isFullscreen,
  onAction,
}) => {
  const [showTooltip, setShowTooltip] = useState<string | null>(null);

  const actions = [
    {
      id: 'copy',
      icon: CopyIcon,
      label: 'Copiar',
      onClick: () => onAction('copy'),
    },
    {
      id: 'download',
      icon: DownloadIcon,
      label: 'Baixar',
      onClick: () => onAction('download'),
    },
    {
      id: 'edit',
      icon: EditIcon,
      label: isEditing ? 'Parar edição' : 'Editar',
      onClick: () => onAction(isEditing ? 'stop-edit' : 'start-edit'),
      active: isEditing,
    },
    {
      id: 'fullscreen',
      icon: isFullscreen ? MinimizeIcon : FullscreenIcon,
      label: isFullscreen ? 'Sair tela cheia' : 'Tela cheia',
      onClick: () => onAction('toggle-fullscreen'),
      active: isFullscreen,
    },
    {
      id: 'close',
      icon: CloseIcon,
      label: 'Fechar',
      onClick: () => onAction('close'),
      variant: 'ghost' as const,
    },
  ];

  return (
    <div className="flex items-center space-x-1">
      {actions.map((action) => (
        <div key={action.id} className="relative">
          <Button
            size="sm"
            variant={action.variant || (action.active ? 'secondary' : 'ghost')}
            onClick={action.onClick}
            className="p-2 h-8 w-8"
            onMouseEnter={() => setShowTooltip(action.id)}
            onMouseLeave={() => setShowTooltip(null)}
            aria-label={action.label}
          >
            <action.icon className="w-3 h-3" />
          </Button>
          
          {/* Tooltip */}
          {showTooltip === action.id && (
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs bg-gray-900 text-white rounded whitespace-nowrap z-10">
              {action.label}
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

// Ícones SVG
const CopyIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
  </svg>
);

const DownloadIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
  </svg>
);

const EditIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
  </svg>
);

const FullscreenIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
  </svg>
);

const MinimizeIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 9l6 6m0-6l-6 6m12-3a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);

const CloseIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
  </svg>
);
