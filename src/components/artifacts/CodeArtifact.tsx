/**
 * Componente para renderizar artefatos de código
 */

import React, { useState, useRef, useEffect } from 'react';
import { ChatMessageArtifact } from '@/types/chat';

interface CodeArtifactProps {
  artifact: ChatMessageArtifact;
  isEditing: boolean;
  onContentChange?: (content: string) => void;
}

const CodeArtifact: React.FC<CodeArtifactProps> = ({
  artifact,
  isEditing,
  onContentChange,
}) => {
  const [editContent, setEditContent] = useState(artifact.content);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Sincronizar conteúdo quando não estiver editando
  useEffect(() => {
    if (!isEditing) {
      setEditContent(artifact.content);
    }
  }, [artifact.content, isEditing]);

  // Auto-resize do textarea
  useEffect(() => {
    if (isEditing && textareaRef.current) {
      const textarea = textareaRef.current;
      textarea.style.height = 'auto';
      textarea.style.height = `${textarea.scrollHeight}px`;
    }
  }, [editContent, isEditing]);

  // Lidar com mudanças no conteúdo
  const handleContentChange = (newContent: string) => {
    setEditContent(newContent);
    onContentChange?.(newContent);
  };

  // Lidar com teclas especiais no editor
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Tab') {
      e.preventDefault();
      const textarea = e.currentTarget;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newContent = editContent.substring(0, start) + '  ' + editContent.substring(end);
      
      setEditContent(newContent);
      onContentChange?.(newContent);
      
      // Restaurar posição do cursor
      setTimeout(() => {
        textarea.selectionStart = textarea.selectionEnd = start + 2;
      }, 0);
    }
  };

  // Obter classe CSS para syntax highlighting baseada na linguagem
  const getLanguageClass = (language?: string) => {
    const baseClasses = 'font-mono text-sm';
    
    switch (language?.toLowerCase()) {
      case 'javascript':
      case 'js':
        return `${baseClasses} language-javascript`;
      case 'typescript':
      case 'ts':
        return `${baseClasses} language-typescript`;
      case 'python':
      case 'py':
        return `${baseClasses} language-python`;
      case 'html':
        return `${baseClasses} language-html`;
      case 'css':
        return `${baseClasses} language-css`;
      case 'json':
        return `${baseClasses} language-json`;
      default:
        return `${baseClasses} language-text`;
    }
  };

  // Renderizar números de linha
  const renderLineNumbers = (content: string) => {
    const lines = content.split('\n');
    return (
      <div className="select-none text-right pr-3 text-theme-muted text-sm font-mono leading-6 bg-theme-secondary border-r border-theme">
        {lines.map((_, index) => (
          <div key={index + 1} className="px-2">
            {index + 1}
          </div>
        ))}
      </div>
    );
  };

  if (isEditing) {
    return (
      <div className="h-full flex flex-col">
        {/* Header do editor */}
        <div className="flex items-center justify-between p-3 bg-theme-secondary border-b border-theme">
          <div className="flex items-center space-x-2 text-sm text-theme-muted">
            <span>Editando</span>
            {artifact.language && (
              <>
                <span>•</span>
                <span>{artifact.language}</span>
              </>
            )}
          </div>
          <div className="text-xs text-theme-muted">
            {editContent.split('\n').length} linhas
          </div>
        </div>

        {/* Editor */}
        <div className="flex-1 overflow-hidden">
          <textarea
            ref={textareaRef}
            value={editContent}
            onChange={(e) => handleContentChange(e.target.value)}
            onKeyDown={handleKeyDown}
            className="w-full h-full p-4 bg-theme-primary text-theme-primary font-mono text-sm resize-none border-0 focus:ring-0 focus:outline-none"
            placeholder="Digite seu código aqui..."
            spellCheck={false}
          />
        </div>
      </div>
    );
  }

  // Modo de visualização
  return (
    <div className="h-full flex flex-col">
      {/* Header da visualização */}
      <div className="flex items-center justify-between p-3 bg-theme-secondary border-b border-theme">
        <div className="flex items-center space-x-2 text-sm text-theme-muted">
          {artifact.language && (
            <span className="px-2 py-1 bg-theme-accent/10 text-theme-accent rounded text-xs font-medium">
              {artifact.language.toUpperCase()}
            </span>
          )}
          <span>{artifact.content.split('\n').length} linhas</span>
        </div>
      </div>

      {/* Visualizador de código */}
      <div className="flex-1 overflow-auto">
        <div className="flex min-h-full">
          {/* Números de linha */}
          {renderLineNumbers(artifact.content)}
          
          {/* Conteúdo do código */}
          <div className="flex-1 overflow-x-auto">
            <pre className={`p-4 ${getLanguageClass(artifact.language)} leading-6 whitespace-pre`}>
              <code className="text-theme-primary">
                {artifact.content}
              </code>
            </pre>
          </div>
        </div>
      </div>

      {/* Footer com estatísticas */}
      <div className="p-2 bg-theme-secondary border-t border-theme text-xs text-theme-muted">
        <div className="flex items-center justify-between">
          <span>
            {artifact.content.length} caracteres
          </span>
          <span>
            Atualizado {formatRelativeTime(artifact.updatedAt)}
          </span>
        </div>
      </div>
    </div>
  );
};

// Formatar tempo relativo
function formatRelativeTime(date: Date): string {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const minutes = Math.floor(diff / 60000);
  
  if (minutes < 1) return 'agora';
  if (minutes < 60) return `${minutes}min atrás`;
  
  const hours = Math.floor(minutes / 60);
  if (hours < 24) return `${hours}h atrás`;
  
  return date.toLocaleDateString('pt-BR');
}

export default CodeArtifact;
