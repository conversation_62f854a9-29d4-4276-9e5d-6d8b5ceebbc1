/**
 * Componente para renderizar artefatos de documento (Markdown)
 */

import React, { useState, useRef, useEffect } from 'react';
import { ChatMessageArtifact } from '@/types/chat';

interface DocumentArtifactProps {
  artifact: ChatMessageArtifact;
  isEditing: boolean;
  onContentChange?: (content: string) => void;
}

const DocumentArtifact: React.FC<DocumentArtifactProps> = ({
  artifact,
  isEditing,
  onContentChange,
}) => {
  const [editContent, setEditContent] = useState(artifact.content);
  const [viewMode, setViewMode] = useState<'preview' | 'markdown'>('preview');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Sincronizar conteúdo
  useEffect(() => {
    if (!isEditing) {
      setEditContent(artifact.content);
    }
  }, [artifact.content, isEditing]);

  // Lidar com mudanças no conteúdo
  const handleContentChange = (newContent: string) => {
    setEditContent(newContent);
    onContentChange?.(newContent);
  };

  // Renderizar Markdown simples para HTML
  const renderMarkdown = (markdown: string): string => {
    return markdown
      // Headers
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      // Bold
      .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
      // Italic
      .replace(/\*(.*)\*/gim, '<em>$1</em>')
      // Code inline
      .replace(/`(.*?)`/gim, '<code>$1</code>')
      // Links
      .replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')
      // Line breaks
      .replace(/\n/gim, '<br>');
  };

  // Extrair título do documento
  const extractTitle = (content: string): string => {
    const lines = content.split('\n');
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed.startsWith('# ')) {
        return trimmed.substring(2);
      }
    }
    return 'Documento';
  };

  if (isEditing) {
    return (
      <div className="h-full flex flex-col">
        {/* Header do editor */}
        <div className="flex items-center justify-between p-3 bg-theme-secondary border-b border-theme">
          <span className="text-sm text-theme-muted">Editando Documento</span>
          <button
            onClick={() => setViewMode(viewMode === 'preview' ? 'markdown' : 'preview')}
            className="px-3 py-1 text-xs bg-theme-accent text-white rounded hover:bg-theme-focus transition-colors"
          >
            {viewMode === 'preview' ? 'Ver markdown' : 'Ver preview'}
          </button>
        </div>

        {/* Editor/Preview */}
        <div className="flex-1 overflow-hidden">
          {viewMode === 'markdown' ? (
            <textarea
              ref={textareaRef}
              value={editContent}
              onChange={(e) => handleContentChange(e.target.value)}
              className="w-full h-full p-4 bg-theme-primary text-theme-primary text-sm resize-none border-0 focus:ring-0 focus:outline-none"
              placeholder="Digite seu documento em Markdown..."
              spellCheck={true}
            />
          ) : (
            <div className="h-full overflow-auto p-4 bg-white dark:bg-gray-900">
              <div 
                className="prose prose-sm max-w-none dark:prose-invert"
                dangerouslySetInnerHTML={{ __html: renderMarkdown(editContent) }}
              />
            </div>
          )}
        </div>
      </div>
    );
  }

  // Modo de visualização
  return (
    <div className="h-full flex flex-col">
      {/* Header da visualização */}
      <div className="flex items-center justify-between p-3 bg-theme-secondary border-b border-theme">
        <div className="flex items-center space-x-2 text-sm text-theme-muted">
          <span className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded text-xs font-medium">
            DOCUMENTO
          </span>
          <span>{extractTitle(artifact.content)}</span>
        </div>
        <button
          onClick={() => setViewMode(viewMode === 'preview' ? 'markdown' : 'preview')}
          className="px-3 py-1 text-xs bg-theme-accent text-white rounded hover:bg-theme-focus transition-colors"
        >
          {viewMode === 'preview' ? 'Ver markdown' : 'Ver preview'}
        </button>
      </div>

      {/* Conteúdo */}
      <div className="flex-1 overflow-auto">
        {viewMode === 'preview' ? (
          <div className="p-4 bg-white dark:bg-gray-900">
            <div 
              className="prose prose-sm max-w-none dark:prose-invert"
              dangerouslySetInnerHTML={{ __html: renderMarkdown(artifact.content) }}
            />
          </div>
        ) : (
          <div className="h-full overflow-auto">
            <pre className="p-4 text-sm leading-6 whitespace-pre-wrap text-theme-primary">
              {artifact.content}
            </pre>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="p-2 bg-theme-secondary border-t border-theme text-xs text-theme-muted">
        <div className="flex items-center justify-between">
          <span>
            {artifact.content.split(' ').length} palavras • {artifact.content.split('\n').length} linhas
          </span>
          <span>
            Atualizado {formatRelativeTime(artifact.updatedAt)}
          </span>
        </div>
      </div>
    </div>
  );
};

// Formatar tempo relativo
function formatRelativeTime(date: Date): string {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const minutes = Math.floor(diff / 60000);
  
  if (minutes < 1) return 'agora';
  if (minutes < 60) return `${minutes}min atrás`;
  
  const hours = Math.floor(minutes / 60);
  if (hours < 24) return `${hours}h atrás`;
  
  return date.toLocaleDateString('pt-BR');
}

export default DocumentArtifact;
