/**
 * Componente para renderizar artefatos HTML
 */

import React, { useState, useRef, useEffect } from 'react';
import { ChatMessageArtifact } from '@/types/chat';

interface HtmlArtifactProps {
  artifact: ChatMessageArtifact;
  isEditing: boolean;
  onContentChange?: (content: string) => void;
}

const HtmlArtifact: React.FC<HtmlArtifactProps> = ({
  artifact,
  isEditing,
  onContentChange,
}) => {
  const [editContent, setEditContent] = useState(artifact.content);
  const [viewMode, setViewMode] = useState<'preview' | 'code'>('preview');
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Sincronizar conteúdo
  useEffect(() => {
    if (!isEditing) {
      setEditContent(artifact.content);
    }
  }, [artifact.content, isEditing]);

  // Atualizar iframe quando o conteúdo mudar
  useEffect(() => {
    if (iframeRef.current && viewMode === 'preview') {
      const iframe = iframeRef.current;

      // Usar srcdoc em vez de document.write para evitar problemas de sandbox
      const sanitizedContent = sanitizeHtml(artifact.content);
      iframe.srcdoc = sanitizedContent;
    }
  }, [artifact.content, viewMode]);

  // Lidar com mudanças no conteúdo
  const handleContentChange = (newContent: string) => {
    setEditContent(newContent);
    onContentChange?.(newContent);
  };

  // Sanitizar HTML para segurança (mais permissivo para desenvolvimento)
  const sanitizeHtml = (html: string): string => {
    // Em desenvolvimento, ser menos restritivo para permitir testes
    if (import.meta.env.DEV) {
      return html;
    }

    // Em produção, manter sanitização rigorosa
    return html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/on\w+="[^"]*"/gi, '')
      .replace(/on\w+='[^']*'/gi, '')
      .replace(/javascript:/gi, '');
  };

  // Extrair título do HTML
  const extractTitle = (html: string): string => {
    const titleMatch = html.match(/<title>(.*?)<\/title>/i);
    if (titleMatch) return titleMatch[1];

    const h1Match = html.match(/<h1[^>]*>(.*?)<\/h1>/i);
    if (h1Match) return h1Match[1].replace(/<[^>]*>/g, '');

    return 'Página HTML';
  };

  if (isEditing) {
    return (
      <div className="h-full flex flex-col">
        {/* Header do editor */}
        <div className="flex items-center justify-between p-3 bg-theme-secondary border-b border-theme">
          <div className="flex items-center space-x-2 text-sm text-theme-muted">
            <span>Editando HTML</span>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode(viewMode === 'preview' ? 'code' : 'preview')}
              className="px-3 py-1 text-xs bg-theme-accent text-white rounded hover:bg-theme-focus transition-colors"
            >
              {viewMode === 'preview' ? 'Ver código' : 'Ver preview'}
            </button>
          </div>
        </div>

        {/* Editor/Preview */}
        <div className="flex-1 overflow-hidden">
          {viewMode === 'code' ? (
            <textarea
              ref={textareaRef}
              value={editContent}
              onChange={(e) => handleContentChange(e.target.value)}
              className="w-full h-full p-4 bg-theme-primary text-theme-primary font-mono text-sm resize-none border-0 focus:ring-0 focus:outline-none"
              placeholder="Digite seu HTML aqui..."
              spellCheck={false}
            />
          ) : (
            <iframe
              ref={iframeRef}
              className="w-full h-full border-0"
              title="Preview HTML"
              sandbox="allow-same-origin allow-scripts allow-forms"
            />
          )}
        </div>
      </div>
    );
  }

  // Modo de visualização
  return (
    <div className="h-full flex flex-col">
      {/* Header da visualização */}
      <div className="flex items-center justify-between p-3 bg-theme-secondary border-b border-theme">
        <div className="flex items-center space-x-2 text-sm text-theme-muted">
          <span className="px-2 py-1 bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200 rounded text-xs font-medium">
            HTML
          </span>
          <span>{extractTitle(artifact.content)}</span>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setViewMode(viewMode === 'preview' ? 'code' : 'preview')}
            className="px-3 py-1 text-xs bg-theme-accent text-white rounded hover:bg-theme-focus transition-colors"
          >
            {viewMode === 'preview' ? 'Ver código' : 'Ver preview'}
          </button>
        </div>
      </div>

      {/* Conteúdo */}
      <div className="flex-1 overflow-hidden">
        {viewMode === 'preview' ? (
          <iframe
            ref={iframeRef}
            className="w-full h-full border-0 bg-white"
            title="Preview HTML"
            sandbox="allow-same-origin allow-scripts allow-forms"
          />
        ) : (
          <div className="h-full overflow-auto">
            <pre className="p-4 font-mono text-sm leading-6 whitespace-pre-wrap text-theme-primary">
              <code>{artifact.content}</code>
            </pre>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="p-2 bg-theme-secondary border-t border-theme text-xs text-theme-muted">
        <div className="flex items-center justify-between">
          <span>
            {artifact.content.length} caracteres • {artifact.content.split('\n').length} linhas
          </span>
          <span>
            Atualizado {formatRelativeTime(artifact.updatedAt)}
          </span>
        </div>
      </div>
    </div>
  );
};

// Formatar tempo relativo
function formatRelativeTime(date: Date): string {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const minutes = Math.floor(diff / 60000);
  
  if (minutes < 1) return 'agora';
  if (minutes < 60) return `${minutes}min atrás`;
  
  const hours = Math.floor(minutes / 60);
  if (hours < 24) return `${hours}h atrás`;
  
  return date.toLocaleDateString('pt-BR');
}

export default HtmlArtifact;
