/**
 * Componente para renderizar artefatos React/JSX
 */

import React, { useState, useRef, useEffect } from 'react';
import { ChatMessageArtifact } from '@/types/chat';

interface ReactArtifactProps {
  artifact: ChatMessageArtifact;
  isEditing: boolean;
  onContentChange?: (content: string) => void;
}

const ReactArtifact: React.FC<ReactArtifactProps> = ({
  artifact,
  isEditing,
  onContentChange,
}) => {
  const [editContent, setEditContent] = useState(artifact.content);
  const [viewMode, setViewMode] = useState<'preview' | 'code'>('code'); // Padrão código para React
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Sincronizar conteúdo
  useEffect(() => {
    if (!isEditing) {
      setEditContent(artifact.content);
    }
  }, [artifact.content, isEditing]);

  // Lidar com mudanças no conteúdo
  const handleContentChange = (newContent: string) => {
    setEditContent(newContent);
    onContentChange?.(newContent);
  };

  // Extrair nome do componente
  const extractComponentName = (content: string): string => {
    // Procurar por export default ComponentName
    const exportMatch = content.match(/export\s+default\s+(\w+)/);
    if (exportMatch) return exportMatch[1];

    // Procurar por function ComponentName
    const functionMatch = content.match(/function\s+(\w+)/);
    if (functionMatch) return functionMatch[1];

    // Procurar por const ComponentName = 
    const constMatch = content.match(/const\s+(\w+)\s*=/);
    if (constMatch) return constMatch[1];

    return 'Componente React';
  };

  if (isEditing) {
    return (
      <div className="h-full flex flex-col">
        {/* Header do editor */}
        <div className="flex items-center justify-between p-3 bg-theme-secondary border-b border-theme">
          <span className="text-sm text-theme-muted">Editando React Component</span>
          <div className="flex items-center space-x-2">
            <span className="text-xs text-theme-muted">Preview não disponível no modo edição</span>
          </div>
        </div>

        {/* Editor */}
        <div className="flex-1 overflow-hidden">
          <textarea
            ref={textareaRef}
            value={editContent}
            onChange={(e) => handleContentChange(e.target.value)}
            className="w-full h-full p-4 bg-theme-primary text-theme-primary font-mono text-sm resize-none border-0 focus:ring-0 focus:outline-none"
            placeholder="Digite seu componente React aqui..."
            spellCheck={false}
          />
        </div>
      </div>
    );
  }

  // Modo de visualização
  return (
    <div className="h-full flex flex-col">
      {/* Header da visualização */}
      <div className="flex items-center justify-between p-3 bg-theme-secondary border-b border-theme">
        <div className="flex items-center space-x-2 text-sm text-theme-muted">
          <span className="px-2 py-1 bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-200 rounded text-xs font-medium">
            REACT
          </span>
          <span>{extractComponentName(artifact.content)}</span>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-xs text-theme-muted">
            Preview requer ambiente de desenvolvimento
          </span>
        </div>
      </div>

      {/* Conteúdo - sempre mostra código para React */}
      <div className="flex-1 overflow-auto">
        <div className="flex min-h-full">
          {/* Números de linha */}
          <div className="select-none text-right pr-3 text-theme-muted text-sm font-mono leading-6 bg-theme-secondary border-r border-theme">
            {artifact.content.split('\n').map((_, index) => (
              <div key={index + 1} className="px-2">
                {index + 1}
              </div>
            ))}
          </div>
          
          {/* Conteúdo do código */}
          <div className="flex-1 overflow-x-auto">
            <pre className="p-4 font-mono text-sm leading-6 whitespace-pre text-theme-primary">
              <code className="language-tsx">
                {artifact.content}
              </code>
            </pre>
          </div>
        </div>
      </div>

      {/* Footer com informações */}
      <div className="p-2 bg-theme-secondary border-t border-theme text-xs text-theme-muted">
        <div className="flex items-center justify-between">
          <span>
            {artifact.content.length} caracteres • {artifact.content.split('\n').length} linhas
          </span>
          <span>
            Atualizado {formatRelativeTime(artifact.updatedAt)}
          </span>
        </div>
      </div>

      {/* Aviso sobre preview */}
      <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border-t border-blue-200 dark:border-blue-800">
        <div className="flex items-center space-x-2 text-blue-800 dark:text-blue-200">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span className="text-xs">
            Para testar este componente, copie o código para seu ambiente de desenvolvimento React.
          </span>
        </div>
      </div>
    </div>
  );
};

// Formatar tempo relativo
function formatRelativeTime(date: Date): string {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const minutes = Math.floor(diff / 60000);
  
  if (minutes < 1) return 'agora';
  if (minutes < 60) return `${minutes}min atrás`;
  
  const hours = Math.floor(minutes / 60);
  if (hours < 24) return `${hours}h atrás`;
  
  return date.toLocaleDateString('pt-BR');
}

export default ReactArtifact;
