/**
 * Componente para renderizar artefatos SVG
 */

import React, { useState, useRef, useEffect } from 'react';
import { ChatMessageArtifact } from '@/types/chat';

interface SvgArtifactProps {
  artifact: ChatMessageArtifact;
  isEditing: boolean;
  onContentChange?: (content: string) => void;
}

const SvgArtifact: React.FC<SvgArtifactProps> = ({
  artifact,
  isEditing,
  onContentChange,
}) => {
  const [editContent, setEditContent] = useState(artifact.content);
  const [viewMode, setViewMode] = useState<'preview' | 'code'>('preview');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Sincronizar conteúdo
  useEffect(() => {
    if (!isEditing) {
      setEditContent(artifact.content);
    }
  }, [artifact.content, isEditing]);

  // Lidar com mudanças no conteúdo
  const handleContentChange = (newContent: string) => {
    setEditContent(newContent);
    onContentChange?.(newContent);
  };

  // Validar se é SVG válido
  const isValidSvg = (content: string): boolean => {
    return content.trim().toLowerCase().includes('<svg') && content.trim().toLowerCase().includes('</svg>');
  };

  // Sanitizar SVG para segurança
  const sanitizeSvg = (svg: string): string => {
    return svg
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/on\w+="[^"]*"/gi, '')
      .replace(/on\w+='[^']*'/gi, '')
      .replace(/javascript:/gi, '');
  };

  if (isEditing) {
    return (
      <div className="h-full flex flex-col">
        {/* Header do editor */}
        <div className="flex items-center justify-between p-3 bg-theme-secondary border-b border-theme">
          <span className="text-sm text-theme-muted">Editando SVG</span>
          <button
            onClick={() => setViewMode(viewMode === 'preview' ? 'code' : 'preview')}
            className="px-3 py-1 text-xs bg-theme-accent text-white rounded hover:bg-theme-focus transition-colors"
          >
            {viewMode === 'preview' ? 'Ver código' : 'Ver preview'}
          </button>
        </div>

        {/* Editor/Preview */}
        <div className="flex-1 overflow-hidden">
          {viewMode === 'code' ? (
            <textarea
              ref={textareaRef}
              value={editContent}
              onChange={(e) => handleContentChange(e.target.value)}
              className="w-full h-full p-4 bg-theme-primary text-theme-primary font-mono text-sm resize-none border-0 focus:ring-0 focus:outline-none"
              placeholder="Digite seu código SVG aqui..."
              spellCheck={false}
            />
          ) : (
            <div className="h-full overflow-auto p-4 bg-white dark:bg-gray-100 flex items-center justify-center">
              {isValidSvg(editContent) ? (
                <div 
                  dangerouslySetInnerHTML={{ __html: sanitizeSvg(editContent) }}
                  className="max-w-full max-h-full"
                />
              ) : (
                <div className="text-center text-gray-500">
                  <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <p>SVG inválido</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  }

  // Modo de visualização
  return (
    <div className="h-full flex flex-col">
      {/* Header da visualização */}
      <div className="flex items-center justify-between p-3 bg-theme-secondary border-b border-theme">
        <div className="flex items-center space-x-2 text-sm text-theme-muted">
          <span className="px-2 py-1 bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 rounded text-xs font-medium">
            SVG
          </span>
          <span>Gráfico Vetorial</span>
        </div>
        <button
          onClick={() => setViewMode(viewMode === 'preview' ? 'code' : 'preview')}
          className="px-3 py-1 text-xs bg-theme-accent text-white rounded hover:bg-theme-focus transition-colors"
        >
          {viewMode === 'preview' ? 'Ver código' : 'Ver preview'}
        </button>
      </div>

      {/* Conteúdo */}
      <div className="flex-1 overflow-hidden">
        {viewMode === 'preview' ? (
          <div className="h-full overflow-auto p-4 bg-white dark:bg-gray-100 flex items-center justify-center">
            {isValidSvg(artifact.content) ? (
              <div 
                dangerouslySetInnerHTML={{ __html: sanitizeSvg(artifact.content) }}
                className="max-w-full max-h-full"
              />
            ) : (
              <div className="text-center text-gray-500">
                <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p>SVG inválido</p>
              </div>
            )}
          </div>
        ) : (
          <div className="h-full overflow-auto">
            <pre className="p-4 font-mono text-sm leading-6 whitespace-pre-wrap text-theme-primary">
              <code>{artifact.content}</code>
            </pre>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="p-2 bg-theme-secondary border-t border-theme text-xs text-theme-muted">
        <div className="flex items-center justify-between">
          <span>
            {artifact.content.length} caracteres • {artifact.content.split('\n').length} linhas
          </span>
          <span>
            Atualizado {formatRelativeTime(artifact.updatedAt)}
          </span>
        </div>
      </div>
    </div>
  );
};

// Formatar tempo relativo
function formatRelativeTime(date: Date): string {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const minutes = Math.floor(diff / 60000);
  
  if (minutes < 1) return 'agora';
  if (minutes < 60) return `${minutes}min atrás`;
  
  const hours = Math.floor(minutes / 60);
  if (hours < 24) return `${hours}h atrás`;
  
  return date.toLocaleDateString('pt-BR');
}

export default SvgArtifact;
