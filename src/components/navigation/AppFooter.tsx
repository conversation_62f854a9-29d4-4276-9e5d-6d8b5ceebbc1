import React from 'react';

interface AppFooterProps {
  className?: string;
}

export const AppFooter: React.FC<AppFooterProps> = ({
  className = '',
}) => {
  return (
    <footer className={`app-footer bg-theme-secondary border-t border-theme mt-auto transition-colors duration-500 ${className}`}>
      <div className="container mx-auto px-4 py-6 text-center text-theme-secondary">
        <p className="text-sm">
          &copy; {new Date().getFullYear()} Flow AdaptEd. Todos os direitos reservados (Demo Conceitual).
        </p>
        <p className="text-xs mt-1">
          Construído com React, TypeScript, Tailwind CSS e Flow AI. Design focado em privacidade.
        </p>
      </div>
    </footer>
  );
};