import React, { createContext, useContext, useEffect, useState, useMemo } from 'react';
import { CognitiveType } from '@/types/cognitive';
import { ThemeMode, CognitivePersona, ThemeConfig } from '@/types/theme';
import { applyPersonaStyles, getCurrentThemeConfig } from '@/lib/theme';
import { userPreferencesService } from '@/services/userPreferencesService';
import { useAuth } from '@/components/AuthProvider';

interface ThemeContextType {
  theme: ThemeMode;
  effectiveTheme: 'light' | 'dark'; // O tema realmente aplicado
  persona: CognitiveType;
  setTheme: (theme: ThemeMode) => Promise<void>;
  setPersona: (persona: CognitiveType) => Promise<void>;
  toggleTheme: () => void;
  getThemeConfig: () => ThemeConfig | null;
  syncWithFirestore: (userId: string) => Promise<void>;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  const [theme, setThemeState] = useState<ThemeMode>(() => {
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem('theme') as ThemeMode;
      if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
        return savedTheme;
      }
    }
    return 'system'; // Padrão para seguir o sistema
  });

  // Calcular o tema efetivo baseado na preferência
  const [effectiveTheme, setEffectiveTheme] = useState<'light' | 'dark'>(() => {
    if (typeof window !== 'undefined') {
      if (theme === 'system') {
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      }
      return theme === 'dark' ? 'dark' : 'light';
    }
    return 'light';
  });

  const [persona, setPersonaState] = useState<CognitiveType>(() => {
    if (typeof window !== 'undefined') {
      const savedPersona = localStorage.getItem('persona') as CognitiveType;
      if (savedPersona && Object.values(CognitiveType).includes(savedPersona)) {
        return savedPersona;
      }
    }
    return CognitiveType.DEFAULT;
  });

  // Carregar preferências do Firebase quando o usuário fizer login
  useEffect(() => {
    const loadUserPreferences = async () => {
      if (user && !isLoading) {
        try {
          setIsLoading(true);
          console.log("🔄 [ThemeProvider] Carregando preferências do usuário do Firebase");

          const preferences = await userPreferencesService.getUserPreferences(user.uid);

          if (preferences) {
            console.log("✅ [ThemeProvider] Preferências carregadas:", preferences);

            // Atualizar tema se diferente do atual
            if (preferences.theme !== theme) {
              setThemeState(preferences.theme);
              localStorage.setItem('theme', preferences.theme);
            }

            // Atualizar persona se diferente da atual
            if (preferences.persona !== persona) {
              setPersonaState(preferences.persona);
              localStorage.setItem('persona', preferences.persona);
            }
          } else {
            console.log("📋 [ThemeProvider] Nenhuma preferência salva encontrada, salvando preferências atuais");
            // Salvar preferências atuais no Firebase se não existirem
            await userPreferencesService.saveUserPreferences(user.uid, { theme, persona });
          }
        } catch (error) {
          console.error("❌ [ThemeProvider] Erro ao carregar preferências:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    loadUserPreferences();
  }, [user]); // Só depende do user, não do theme/persona para evitar loops

  const setTheme = async (newTheme: ThemeMode) => {
    setThemeState(newTheme);
    localStorage.setItem('theme', newTheme);

    // Sincronizar com Firebase se o usuário estiver logado
    if (user && !isLoading) {
      try {
        await userPreferencesService.updateUserTheme(user.uid, newTheme);
        console.log("✅ [ThemeProvider] Tema sincronizado com Firebase:", newTheme);
      } catch (error) {
        console.error("❌ [ThemeProvider] Erro ao sincronizar tema:", error);
      }
    }
  };

  const setPersona = async (newPersona: CognitiveType) => {
    setPersonaState(newPersona);
    localStorage.setItem('persona', newPersona);

    // Sincronizar com Firebase se o usuário estiver logado
    if (user && !isLoading) {
      try {
        await userPreferencesService.updateUserPersona(user.uid, newPersona);
        console.log("✅ [ThemeProvider] Persona sincronizada com Firebase:", newPersona);
      } catch (error) {
        console.error("❌ [ThemeProvider] Erro ao sincronizar persona:", error);
      }
    }
  };

  const toggleTheme = () => {
    let nextTheme: ThemeMode;
    if (theme === 'light') {
      nextTheme = 'dark';
    } else if (theme === 'dark') {
      nextTheme = 'system';
    } else {
      nextTheme = 'light';
    }
    setTheme(nextTheme);
  };

  const getThemeConfig = (): ThemeConfig | null => {
    return getCurrentThemeConfig();
  };

  const syncWithFirestore = async (userId: string): Promise<void> => {
    try {
      setIsLoading(true);
      console.log("🔄 [ThemeProvider] Sincronizando preferências com Firebase");

      const preferences = await userPreferencesService.getUserPreferences(userId);

      if (preferences) {
        console.log("✅ [ThemeProvider] Preferências carregadas do Firebase:", preferences);

        // Atualizar tema se diferente do atual
        if (preferences.theme !== theme) {
          setThemeState(preferences.theme);
          localStorage.setItem('theme', preferences.theme);
        }

        // Atualizar persona se diferente da atual
        if (preferences.persona !== persona) {
          setPersonaState(preferences.persona);
          localStorage.setItem('persona', preferences.persona);
        }
      } else {
        // Salvar preferências atuais no Firebase se não existirem
        await userPreferencesService.saveUserPreferences(userId, { theme, persona });
        console.log("📋 [ThemeProvider] Preferências atuais salvas no Firebase");
      }
    } catch (error) {
      console.error("❌ [ThemeProvider] Erro ao sincronizar com Firebase:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Atualizar tema efetivo quando o tema ou preferência do sistema mudar
  useEffect(() => {
    const updateEffectiveTheme = () => {
      if (theme === 'system') {
        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        setEffectiveTheme(systemPrefersDark ? 'dark' : 'light');
      } else {
        setEffectiveTheme(theme === 'dark' ? 'dark' : 'light');
      }
    };

    updateEffectiveTheme();

    // Escutar mudanças na preferência do sistema
    if (theme === 'system') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.addEventListener('change', updateEffectiveTheme);
      return () => mediaQuery.removeEventListener('change', updateEffectiveTheme);
    }
  }, [theme]);

  useEffect(() => {
    // Aplicar estilos da persona e tema usando o novo sistema
    applyPersonaStyles(persona as CognitivePersona, effectiveTheme);
  }, [effectiveTheme, persona]);

  // Remover o useEffect antigo pois agora é tratado no useEffect acima

  const value: ThemeContextType = useMemo(() => ({
    theme,
    effectiveTheme,
    persona,
    setTheme,
    setPersona,
    toggleTheme,
    getThemeConfig,
    syncWithFirestore,
  }), [theme, effectiveTheme, persona]);

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme deve ser usado dentro de um ThemeProvider');
  }
  return context;
};