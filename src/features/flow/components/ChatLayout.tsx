/**
 * Layout responsivo para chat com artefatos
 * Desktop: Chat à esquerda, Artefato à direita
 * Mobile: Chat a<PERSON>ma, Artefato abaixo
 */

import React from 'react';
import { ArtifactLayoutConfig } from '@/types/artifacts';
import { ChatMessageArtifact } from '@/types/chat';
import { ArtifactContainer } from '@/components/artifacts/ArtifactContainer';

interface ChatLayoutProps {
  // Conteúdo do chat
  chatContent: React.ReactNode;
  
  // Artefato ativo
  activeArtifact: ChatMessageArtifact | null;
  layoutConfig: ArtifactLayoutConfig;
  isEditingArtifact: boolean;
  
  // Ações do artefato
  onArtifactAction: (action: string, data?: any) => void;
  
  // Classes CSS adicionais
  className?: string;
}

export const ChatLayout: React.FC<ChatLayoutProps> = ({
  chatContent,
  activeArtifact,
  layoutConfig,
  isEditingArtifact,
  onArtifactAction,
  className = '',
}) => {
  const { showArtifact, artifactPosition, isFullscreen, artifactWidth = '50%' } = layoutConfig;

  // Se não há artefato, mostrar apenas o chat
  if (!showArtifact || !activeArtifact) {
    return (
      <div className={`chat-layout-single h-full ${className}`}>
        {chatContent}
      </div>
    );
  }

  // Layout fullscreen do artefato
  if (isFullscreen) {
    return (
      <div className="chat-layout-fullscreen h-full relative">
        {/* Chat minimizado no canto */}
        <div className="absolute top-4 left-4 w-80 max-h-96 bg-theme-primary border border-theme rounded-lg shadow-lg z-40 overflow-hidden">
          <div className="p-2 bg-theme-secondary border-b border-theme flex items-center justify-between">
            <span className="text-xs font-medium text-theme-primary">Chat</span>
            <button
              onClick={() => onArtifactAction('toggle-fullscreen')}
              className="text-xs text-theme-muted hover:text-theme-primary"
            >
              Expandir
            </button>
          </div>
          <div className="max-h-80 overflow-y-auto">
            {chatContent}
          </div>
        </div>

        {/* Artefato em fullscreen */}
        <ArtifactContainer
          artifact={activeArtifact}
          layoutConfig={layoutConfig}
          isEditing={isEditingArtifact}
          onAction={onArtifactAction}
          className="h-full"
        />
      </div>
    );
  }

  // Layout desktop (lado a lado)
  if (artifactPosition === 'right') {
    return (
      <div className={`chat-layout-desktop h-full flex ${className}`}>
        {/* Chat à esquerda */}
        <div 
          className="chat-section flex-shrink-0 border-r border-theme"
          style={{ width: `calc(100% - ${artifactWidth})` }}
        >
          {chatContent}
        </div>

        {/* Artefato à direita */}
        <div 
          className="artifact-section flex-shrink-0"
          style={{ width: artifactWidth }}
        >
          <ArtifactContainer
            artifact={activeArtifact}
            layoutConfig={layoutConfig}
            isEditing={isEditingArtifact}
            onAction={onArtifactAction}
            className="h-full"
          />
        </div>
      </div>
    );
  }

  // Layout mobile (empilhado)
  return (
    <div className={`chat-layout-mobile h-full flex flex-col ${className}`}>
      {/* Chat acima */}
      <div className="chat-section flex-1 min-h-0 border-b border-theme">
        {chatContent}
      </div>

      {/* Artefato abaixo */}
      <div className="artifact-section h-1/2 flex-shrink-0">
        <ArtifactContainer
          artifact={activeArtifact}
          layoutConfig={layoutConfig}
          isEditing={isEditingArtifact}
          onAction={onArtifactAction}
          className="h-full"
        />
      </div>
    </div>
  );
};
