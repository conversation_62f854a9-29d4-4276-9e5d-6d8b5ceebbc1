import React from 'react';
import { detectMessageContentType, formatCodeContent } from '../utils/messageFormatters';
import { createArtifact } from '../utils/artifactDetector';
import { ChatMessageArtifact } from '@/types/chat';

interface MessageContentRendererProps {
  text: string;
  messageId?: string;
  onArtifactCreated?: (artifact: ChatMessageArtifact) => void;
}

export const MessageContentRenderer: React.FC<MessageContentRendererProps> = ({
  text,
  messageId,
  onArtifactCreated
}) => {
  const contentType = detectMessageContentType(text);
  const [artifactCreated, setArtifactCreated] = React.useState(false);

  // Verificar se deve criar artefato (apenas uma vez por mensagem)
  React.useEffect(() => {
    if (messageId && onArtifactCreated && !artifactCreated && text.length > 100) {
      const artifact = createArtifact(text, messageId);
      if (artifact) {
        onArtifactCreated(artifact);
        setArtifactCreated(true);
      }
    }
  }, [messageId, onArtifactCreated, artifactCreated, text]);

  // Renderizar código
  if (contentType === 'code') {
    const parts = text.split(/(```[\s\S]*?```)/);
    return (
      <>
        {parts.map((part) => {
          if (part.startsWith('```') && part.endsWith('```')) {
            const { language, codeContent } = formatCodeContent(part);
            // Usar hash simples do conteúdo para key única
            const contentHash = btoa(part.slice(0, 50)).replace(/[^a-zA-Z0-9]/g, '');

            return (
              <div key={`code-${contentHash}`} className="my-2 bg-gray-900 text-green-400 rounded p-3 font-mono text-sm overflow-x-auto">
                {language && (
                  <div className="text-xs text-gray-400 mb-2 border-b border-gray-700 pb-1">
                    {language}
                  </div>
                )}
                <pre className="whitespace-pre-wrap">{codeContent}</pre>
              </div>
            );
          }
          // Para texto simples, usar hash do conteúdo
          const textHash = btoa(part.slice(0, 30)).replace(/[^a-zA-Z0-9]/g, '');
          return <span key={`text-${textHash}`}>{part}</span>;
        })}
      </>
    );
  }

  // Detectar solicitações de imagem
  if (contentType === 'image-request') {
    return (
      <div>
        {text}
        <div className="mt-2 p-3 bg-blue-100 dark:bg-blue-900 rounded-lg border border-blue-300 dark:border-blue-700">
          <div className="flex items-center space-x-2">
            <span className="text-2xl">🎨</span>
            <div>
              <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                Geração de Imagens
              </p>
              <p className="text-xs text-blue-600 dark:text-blue-300">
                Esta funcionalidade será implementada em breve. Por enquanto, posso ajudar com descrições detalhadas.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Detectar solicitações de artefatos interativos
  if (contentType === 'interactive-request') {
    return (
      <div>
        {text}
        <div className="mt-2 p-3 bg-purple-100 dark:bg-purple-900 rounded-lg border border-purple-300 dark:border-purple-700">
          <div className="flex items-center space-x-2">
            <span className="text-2xl">⚡</span>
            <div>
              <p className="text-sm font-medium text-purple-800 dark:text-purple-200">
                Artefatos Interativos
              </p>
              <p className="text-xs text-purple-600 dark:text-purple-300">
                Em breve poderei criar componentes interativos. Por enquanto, posso fornecer o código completo.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Texto simples
  return <>{text}</>;
};