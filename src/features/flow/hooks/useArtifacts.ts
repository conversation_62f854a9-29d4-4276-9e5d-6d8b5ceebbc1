/**
 * Hook para gerenciar estado e ações dos artefatos
 */

import { useReducer, useCallback, useEffect } from 'react';
import { ChatMessageArtifact } from '@/types/chat';
import { 
  ArtifactState, 
  ArtifactActionType, 
  ArtifactLayoutConfig 
} from '@/types/artifacts';

// Estado inicial
const initialState: ArtifactState = {
  activeArtifactId: null,
  artifacts: new Map(),
  layoutConfig: {
    showArtifact: false,
    artifactPosition: 'right', // Será ajustado baseado na tela
  },
  isEditing: false,
  editingArtifactId: null,
};

// Cache para evitar criação duplicada
const createdArtifacts = new Set<string>();

// Reducer para gerenciar estado dos artefatos
function artifactReducer(state: ArtifactState, action: ArtifactActionType): ArtifactState {
  switch (action.type) {
    case 'CREATE_ARTIFACT': {
      const newArtifacts = new Map(state.artifacts);
      newArtifacts.set(action.payload.id, action.payload);
      return {
        ...state,
        artifacts: newArtifacts,
        activeArtifactId: action.payload.id,
        layoutConfig: {
          ...state.layoutConfig,
          showArtifact: true,
        },
      };
    }

    case 'UPDATE_ARTIFACT': {
      if (!state.artifacts.has(action.payload.id)) return state;

      const updatedArtifacts = new Map(state.artifacts);
      const currentArtifact = updatedArtifacts.get(action.payload.id)!;
      const updatedArtifact = {
        ...currentArtifact,
        ...action.payload.updates,
        updatedAt: new Date(),
      };
      updatedArtifacts.set(action.payload.id, updatedArtifact);

      return {
        ...state,
        artifacts: updatedArtifacts,
      };
    }

    case 'DELETE_ARTIFACT': {
      const filteredArtifacts = new Map(state.artifacts);
      filteredArtifacts.delete(action.payload);

      return {
        ...state,
        artifacts: filteredArtifacts,
        activeArtifactId: state.activeArtifactId === action.payload ? null : state.activeArtifactId,
        layoutConfig: {
          ...state.layoutConfig,
          showArtifact: filteredArtifacts.size > 0 && state.activeArtifactId !== action.payload,
        },
      };
    }

    case 'SET_ACTIVE_ARTIFACT':
      return {
        ...state,
        activeArtifactId: action.payload,
        layoutConfig: {
          ...state.layoutConfig,
          showArtifact: action.payload !== null,
        },
      };

    case 'SET_LAYOUT_CONFIG':
      return {
        ...state,
        layoutConfig: {
          ...state.layoutConfig,
          ...action.payload,
        },
      };

    case 'START_EDITING':
      return {
        ...state,
        isEditing: true,
        editingArtifactId: action.payload,
      };

    case 'STOP_EDITING':
      return {
        ...state,
        isEditing: false,
        editingArtifactId: null,
      };

    case 'ADD_VERSION': {
      if (!state.artifacts.has(action.payload.artifactId)) return state;

      const versionArtifacts = new Map(state.artifacts);
      const artifact = versionArtifacts.get(action.payload.artifactId)!;
      const updatedVersions = [...(artifact.versions || []), action.payload.version];

      versionArtifacts.set(action.payload.artifactId, {
        ...artifact,
        versions: updatedVersions,
        updatedAt: new Date(),
      });

      return {
        ...state,
        artifacts: versionArtifacts,
      };
    }

    default:
      return state;
  }
}

// Hook principal
export function useArtifacts() {
  const [state, dispatch] = useReducer(artifactReducer, initialState);

  // Detectar tamanho da tela e ajustar layout
  useEffect(() => {
    const updateLayout = () => {
      const isMobile = window.innerWidth < 768;
      dispatch({
        type: 'SET_LAYOUT_CONFIG',
        payload: {
          artifactPosition: isMobile ? 'bottom' : 'right',
        },
      });
    };

    updateLayout();
    window.addEventListener('resize', updateLayout);
    return () => window.removeEventListener('resize', updateLayout);
  }, []);

  // Ações
  const createArtifact = useCallback((artifact: ChatMessageArtifact) => {
    // Evitar criação duplicada
    const artifactKey = `${artifact.type}-${artifact.content.substring(0, 100)}`;
    if (createdArtifacts.has(artifactKey)) {
      return;
    }

    createdArtifacts.add(artifactKey);
    dispatch({ type: 'CREATE_ARTIFACT', payload: artifact });
  }, []);

  const updateArtifact = useCallback((id: string, updates: Partial<ChatMessageArtifact>) => {
    dispatch({ type: 'UPDATE_ARTIFACT', payload: { id, updates } });
  }, []);

  const deleteArtifact = useCallback((id: string) => {
    dispatch({ type: 'DELETE_ARTIFACT', payload: id });
  }, []);

  const setActiveArtifact = useCallback((id: string | null) => {
    dispatch({ type: 'SET_ACTIVE_ARTIFACT', payload: id });
  }, []);

  const setLayoutConfig = useCallback((config: Partial<ArtifactLayoutConfig>) => {
    dispatch({ type: 'SET_LAYOUT_CONFIG', payload: config });
  }, []);

  const startEditing = useCallback((id: string) => {
    dispatch({ type: 'START_EDITING', payload: id });
  }, []);

  const stopEditing = useCallback(() => {
    dispatch({ type: 'STOP_EDITING' });
  }, []);

  const addVersion = useCallback((artifactId: string, version: any) => {
    dispatch({ type: 'ADD_VERSION', payload: { artifactId, version } });
  }, []);

  // Getters
  const getActiveArtifact = useCallback(() => {
    if (!state.activeArtifactId) return null;
    return state.artifacts.get(state.activeArtifactId) || null;
  }, [state.activeArtifactId, state.artifacts]);

  const getArtifact = useCallback((id: string) => {
    return state.artifacts.get(id) || null;
  }, [state.artifacts]);

  const getAllArtifacts = useCallback(() => {
    return Array.from(state.artifacts.values());
  }, [state.artifacts]);

  // Utilitários
  const copyArtifactContent = useCallback(async (id: string) => {
    const artifact = state.artifacts.get(id);
    if (!artifact) return false;

    try {
      await navigator.clipboard.writeText(artifact.content);
      return true;
    } catch (error) {
      console.error('Erro ao copiar conteúdo:', error);
      return false;
    }
  }, [state.artifacts]);

  const downloadArtifact = useCallback((id: string) => {
    const artifact = state.artifacts.get(id);
    if (!artifact) return;

    const blob = new Blob([artifact.content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    
    a.href = url;
    a.download = `${artifact.title || 'artifact'}.${getFileExtension(artifact)}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [state.artifacts]);

  const toggleFullscreen = useCallback(() => {
    setLayoutConfig({
      isFullscreen: !state.layoutConfig.isFullscreen,
    });
  }, [state.layoutConfig.isFullscreen, setLayoutConfig]);

  const closeArtifact = useCallback(() => {
    setActiveArtifact(null);
  }, [setActiveArtifact]);

  return {
    // Estado
    ...state,
    
    // Ações
    createArtifact,
    updateArtifact,
    deleteArtifact,
    setActiveArtifact,
    setLayoutConfig,
    startEditing,
    stopEditing,
    addVersion,
    
    // Getters
    getActiveArtifact,
    getArtifact,
    getAllArtifacts,
    
    // Utilitários
    copyArtifactContent,
    downloadArtifact,
    toggleFullscreen,
    closeArtifact,
  };
}

// Utilitário para obter extensão de arquivo
function getFileExtension(artifact: ChatMessageArtifact): string {
  switch (artifact.type) {
    case 'code':
      switch (artifact.language) {
        case 'javascript': return 'js';
        case 'typescript': return 'ts';
        case 'python': return 'py';
        case 'html': return 'html';
        case 'css': return 'css';
        case 'json': return 'json';
        default: return 'txt';
      }
    case 'react': return 'tsx';
    case 'html': return 'html';
    case 'document': return 'md';
    case 'svg': return 'svg';
    default: return 'txt';
  }
}
