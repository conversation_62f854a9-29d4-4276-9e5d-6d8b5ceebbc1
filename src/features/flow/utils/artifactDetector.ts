/**
 * Detector de artefatos - identifica quando criar artefatos automaticamente
 */

import { ChatMessageArtifact } from '@/types/chat';
import { 
  ArtifactCreationCriteria, 
  DEFAULT_ARTIFACT_CRITERIA, 
  ARTIFACT_TRIGGERS 
} from '@/types/artifacts';

// Detecta se uma mensagem deve gerar um artefato
export function shouldCreateArtifact(
  text: string, 
  criteria: ArtifactCreationCriteria = DEFAULT_ARTIFACT_CRITERIA
): boolean {
  // Verificações básicas de tamanho
  const lines = text.split('\n').length;
  const length = text.length;

  if (lines < criteria.minLines && length < criteria.minLength) {
    return false;
  }

  // Verificar se tem estrutura definida
  if (criteria.hasStructure && !hasDefinedStructure(text)) {
    return false;
  }

  // Verificar se é conteúdo independente
  if (criteria.isStandalone && !isStandaloneContent(text)) {
    return false;
  }

  return true;
}

// Detecta o tipo de artefato baseado no conteúdo
export function detectArtifactType(text: string): ChatMessageArtifact['type'] | null {
  const lowerText = text.toLowerCase();
  
  // Código com blocos de código
  if (text.includes('```')) {
    const codeBlocks = text.match(/```[\s\S]*?```/g);
    if (codeBlocks && codeBlocks.some(block => block.split('\n').length > 10)) {
      return 'code';
    }
  }

  // React/JSX
  if (ARTIFACT_TRIGGERS.REACT.some(trigger => text.includes(trigger))) {
    return 'react';
  }

  // HTML
  if (ARTIFACT_TRIGGERS.HTML.some(trigger => lowerText.includes(trigger.toLowerCase()))) {
    return 'html';
  }

  // SVG
  if (ARTIFACT_TRIGGERS.SVG.some(trigger => lowerText.includes(trigger.toLowerCase()))) {
    return 'svg';
  }

  // Documento estruturado
  if (ARTIFACT_TRIGGERS.DOCUMENT.some(trigger => text.includes(trigger))) {
    const structuredLines = text.split('\n').filter(line => 
      ARTIFACT_TRIGGERS.DOCUMENT.some(trigger => line.trim().startsWith(trigger))
    );
    if (structuredLines.length > 3) {
      return 'document';
    }
  }

  // Código sem blocos (baseado em palavras-chave)
  if (ARTIFACT_TRIGGERS.CODE.some(trigger => text.includes(trigger))) {
    return 'code';
  }

  return null;
}

// Extrai informações específicas do artefato
export function extractArtifactInfo(text: string, type: ChatMessageArtifact['type']) {
  const info: Partial<ChatMessageArtifact> = {
    type,
    content: text,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  switch (type) {
    case 'code':
    case 'react':
      const codeInfo = extractCodeInfo(text);
      info.language = codeInfo.language;
      info.title = codeInfo.title;
      info.content = codeInfo.content;
      break;

    case 'html':
      info.title = extractHtmlTitle(text);
      info.content = text;
      break;

    case 'document':
      info.title = extractDocumentTitle(text);
      info.content = text;
      break;

    case 'svg':
      info.title = 'Gráfico SVG';
      info.content = text;
      break;
  }

  return info;
}

// Extrai informações de código
function extractCodeInfo(text: string) {
  let language = 'text';
  let title = 'Código';
  let content = text;

  // Detectar linguagem de blocos de código
  const codeBlockMatch = text.match(/```(\w+)?\n([\s\S]*?)```/);
  if (codeBlockMatch) {
    language = codeBlockMatch[1] || 'text';
    content = codeBlockMatch[2];
    
    // Gerar título baseado na linguagem
    const languageNames: Record<string, string> = {
      javascript: 'JavaScript',
      typescript: 'TypeScript',
      python: 'Python',
      html: 'HTML',
      css: 'CSS',
      json: 'JSON',
      jsx: 'React JSX',
      tsx: 'React TSX',
    };
    title = languageNames[language] || `Código ${language}`;
  }

  // Detectar função ou classe principal
  const functionMatch = content.match(/(?:function|class|const|let|var)\s+(\w+)/);
  if (functionMatch) {
    title = `${title} - ${functionMatch[1]}`;
  }

  return { language, title, content };
}

// Extrai título de HTML
function extractHtmlTitle(text: string): string {
  const titleMatch = text.match(/<title>(.*?)<\/title>/i);
  if (titleMatch) {
    return titleMatch[1];
  }

  const h1Match = text.match(/<h1[^>]*>(.*?)<\/h1>/i);
  if (h1Match) {
    return h1Match[1].replace(/<[^>]*>/g, ''); // Remove tags HTML
  }

  return 'Página HTML';
}

// Extrai título de documento
function extractDocumentTitle(text: string): string {
  const lines = text.split('\n');
  
  // Procurar por título principal (# Título)
  for (const line of lines) {
    const trimmed = line.trim();
    if (trimmed.startsWith('# ')) {
      return trimmed.substring(2);
    }
  }

  // Procurar por primeira linha não vazia
  for (const line of lines) {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#') && !trimmed.startsWith('-')) {
      return trimmed.length > 50 ? trimmed.substring(0, 47) + '...' : trimmed;
    }
  }

  return 'Documento';
}

// Verifica se tem estrutura definida
function hasDefinedStructure(text: string): boolean {
  // Código com blocos
  if (text.includes('```')) return true;
  
  // HTML/XML tags
  if (/<[^>]+>/.test(text)) return true;
  
  // Markdown estruturado
  if (/^#{1,6}\s/.test(text) || /^\d+\.\s/.test(text) || /^[-*+]\s/.test(text)) return true;
  
  // JSON/objeto estruturado
  if (/^\s*[{\[]/.test(text.trim())) return true;
  
  return false;
}

// Verifica se é conteúdo independente
function isStandaloneContent(text: string): boolean {
  // Muito curto para ser independente
  if (text.length < 100) return false;
  
  // Código completo
  if (text.includes('```') || text.includes('function') || text.includes('class')) return true;
  
  // HTML completo
  if (text.includes('<!DOCTYPE') || text.includes('<html')) return true;
  
  // Documento estruturado
  if (text.split('\n').filter(line => line.trim().startsWith('#')).length > 1) return true;
  
  return false;
}

// Cria um artefato completo
export function createArtifact(
  text: string, 
  messageId: string,
  promptUsed?: string
): ChatMessageArtifact | null {
  if (!shouldCreateArtifact(text)) {
    return null;
  }

  const type = detectArtifactType(text);
  if (!type) {
    return null;
  }

  const info = extractArtifactInfo(text, type);
  
  return {
    id: `artifact-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    status: 'loaded',
    promptUsed,
    versions: [],
    ...info,
  } as ChatMessageArtifact;
}
