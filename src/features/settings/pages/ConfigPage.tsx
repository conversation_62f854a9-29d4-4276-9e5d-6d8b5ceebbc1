import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useFlow } from '@/components/FlowProvider';
import { useForm } from '@/hooks/useForm';
import { z } from 'zod';
import { FlowConfig } from '@/types/flow';
import { Card } from '@/components/ui/Card';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { Alert } from '@/components/ui/Alert';
import { DashboardLayout } from '@/layouts/DashboardLayout';
import { env } from '@/lib/env';

// Schema de validação para o formulário
const configSchema = z.object({
  flowBaseUrl: z.string().url('URL base inválida'),
  flowTenant: z.string().min(1, 'Tenant é obrigatório'),
  flowClientId: z.string().uuid('ID de cliente inválido'),
  flowClientSecret: z.string().min(1, 'Secret é obrigatório'),
  flowAppToAccess: z.string().min(1, 'App é obrigatório'),
  flowAgent: z.string().optional(),
  apiModelId: z.string().optional(),
  modelTemperature: z.number().min(0).max(1).optional(),
});

const ConfigPage: React.FC = () => {
  const navigate = useNavigate();
  const { configure, getConfig, isConfigured, error: flowError } = useFlow();
  const [success, setSuccess] = useState<string | null>(null);

  // Obter valores iniciais da configuração atual ou do .env
  const initialValues: FlowConfig = {
    flowBaseUrl: env.VITE_FLOW_BASE_URL || '',
    flowTenant: env.VITE_FLOW_TENANT || '',
    flowClientId: env.VITE_FLOW_CLIENT_ID || '',
    flowClientSecret: env.VITE_FLOW_CLIENT_SECRET || '',
    flowAppToAccess: env.VITE_FLOW_APP_TO_ACCESS || '',
    flowAgent: '',
    apiModelId: '',
    modelTemperature: 0.7,
  };

  // Carregar configurações existentes, se houver
  useEffect(() => {
    if (isConfigured) {
      const currentConfig = getConfig();
      
      // Atualizar os campos do formulário com os valores atuais
      Object.keys(currentConfig).forEach((key) => {
        const configKey = key as keyof FlowConfig;
        setFieldValue(configKey, currentConfig[configKey] || initialValues[configKey]);
      });
    }
  }, [isConfigured]);

  // Configurar formulário com validação
  const {
    values,
    errors,
    handleChange,
    handleSubmit,
    setFieldValue,
    isSubmitting,
  } = useForm({
    initialValues,
    validationSchema: configSchema,
    onSubmit: async (values) => {
      setSuccess(null);
      try {
        await configure(values);
        setSuccess('Configurações salvas com sucesso!');
        
        // Redirecionar após 2 segundos
        setTimeout(() => {
          navigate('/');
        }, 2000);
      } catch (error) {
        // Erro já é tratado pelo hook useFlow
        console.error('Erro ao salvar configurações:', error);
      }
    },
  });

  return (
    <DashboardLayout>
      <h1 className="text-2xl font-bold text-gray-900 mb-6">Configurações do Flow</h1>

      {flowError && (
        <Alert 
          variant="error" 
          className="mb-4"
          title="Erro"
          onClose={() => console.log('Fechar erro')}
        >
          {flowError}
        </Alert>
      )}

      {success && (
        <Alert 
          variant="success" 
          className="mb-4"
          title="Sucesso"
          onClose={() => setSuccess(null)}
        >
          {success}
        </Alert>
      )}

      <Card>
        <form onSubmit={handleSubmit} className="space-y-4 p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input 
              label="URL Base do Flow"
              name="flowBaseUrl"
              value={values.flowBaseUrl}
              onChange={handleChange}
              error={errors.flowBaseUrl}
              placeholder="https://flow.ciandt.com"
            />

            <Input 
              label="Tenant"
              name="flowTenant"
              value={values.flowTenant}
              onChange={handleChange}
              error={errors.flowTenant}
              placeholder="cit"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input 
              label="Client ID (UUID)"
              name="flowClientId"
              value={values.flowClientId}
              onChange={handleChange}
              error={errors.flowClientId}
              placeholder="00000000-0000-0000-0000-000000000000"
            />

            <Input 
              label="Client Secret"
              name="flowClientSecret"
              type="password"
              value={values.flowClientSecret}
              onChange={handleChange}
              error={errors.flowClientSecret}
              placeholder="Digite o client secret"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input 
              label="App to Access"
              name="flowAppToAccess"
              value={values.flowAppToAccess}
              onChange={handleChange}
              error={errors.flowAppToAccess}
              placeholder="llm-api"
            />

            <Input 
              label="Agente (opcional)"
              name="flowAgent"
              value={values.flowAgent || ''}
              onChange={handleChange}
              error={errors.flowAgent}
              placeholder="Nome do agente (opcional)"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input 
              label="ID do Modelo (opcional)"
              name="apiModelId"
              value={values.apiModelId || ''}
              onChange={handleChange}
              error={errors.apiModelId}
              placeholder="gpt-4, gemini-pro, claude-3, etc."
            />

            <Input 
              label="Temperatura do modelo (0-1)"
              name="modelTemperature"
              type="number"
              min="0"
              max="1"
              step="0.1"
              value={String(values.modelTemperature || 0.7)}
              onChange={handleChange}
              error={errors.modelTemperature}
            />
          </div>

          <div className="flex justify-end space-x-4 pt-4">
            <Button 
              type="button" 
              variant="outline"
              onClick={() => navigate('/')}
            >
              Cancelar
            </Button>
            <Button 
              type="submit"
              isLoading={isSubmitting}
            >
              Salvar configurações
            </Button>
          </div>
        </form>
      </Card>
    </DashboardLayout>
  );
};

export default ConfigPage;
