@tailwind base;
@tailwind components;
@tailwind utilities;

/* Garantir que classes essenciais sejam incluídas */
@layer utilities {
  .h-8 { height: 2rem; }
  .w-8 { width: 2rem; }
  .h-12 { height: 3rem; }
  .h-16 { width: 4rem; }
  .w-16 { width: 4rem; }
  .h-5 { height: 1.25rem; }
  .w-5 { width: 1.25rem; }
  .space-x-3 > :not([hidden]) ~ :not([hidden]) { margin-left: 0.75rem; }
  .space-x-2 > :not([hidden]) ~ :not([hidden]) { margin-left: 0.5rem; }
  .px-4 { padding-left: 1rem; padding-right: 1rem; }
  .py-4 { padding-top: 1rem; padding-bottom: 1rem; }
  .px-8 { padding-left: 2rem; padding-right: 2rem; }
  .pb-8 { padding-bottom: 2rem; }
  .pb-6 { padding-bottom: 1.5rem; }
  .p-8 { padding: 2rem; }
  .pt-20 { padding-top: 5rem; }
  .mb-4 { margin-bottom: 1rem; }
  .mb-2 { margin-bottom: 0.5rem; }
  .mb-6 { margin-bottom: 1.5rem; }
  .mx-auto { margin-left: auto; margin-right: auto; }
  .max-w-7xl { max-width: 80rem; }
  .max-w-lg { max-width: 32rem; }
  .text-xl { font-size: 1.25rem; line-height: 1.75rem; }
  .text-2xl { font-size: 1.5rem; line-height: 2rem; }
  .text-xs { font-size: 0.75rem; line-height: 1rem; }
  .text-sm { font-size: 0.875rem; line-height: 1.25rem; }
  .font-bold { font-weight: 700; }
  .font-medium { font-weight: 500; }
  .rounded-lg { border-radius: 0.5rem; }
  .rounded-full { border-radius: 9999px; }
  .shadow-sm { box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05); }
  .shadow-md { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); }
  .shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); }
  .shadow-xl { box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04); }
  .shadow-2xl { box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25); }
  .hover\:shadow-md:hover { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); }
  .hover\:shadow-2xl:hover { box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25); }
  .animate-spin { animation: spin 1s linear infinite; }
  .animate-fadeInUp { animation: fadeInUp 0.6s ease-out forwards; }
}

/* ========================================
   SISTEMA DE TEMAS ADAPTATIVO - CSS VARIABLES
   ======================================== */

:root {
  /* ===== TEMA GLOBAL CLARO (DEFAULT) ===== */

  /* Cores Primárias */
  --color-primary: 59, 130, 246; /* blue-500 */
  --color-primary-hover: 37, 99, 235; /* blue-600 */
  --color-primary-light: 147, 197, 253; /* blue-300 */
  --color-primary-dark: 29, 78, 216; /* blue-700 */

  /* Cores Secundárias */
  --color-secondary: 107, 114, 128; /* gray-500 */
  --color-secondary-hover: 75, 85, 99; /* gray-600 */
  --color-secondary-light: 156, 163, 175; /* gray-400 */

  /* Cores de Destaque */
  --color-accent: 34, 197, 94; /* green-500 */
  --color-accent-hover: 22, 163, 74; /* green-600 */
  --color-accent-light: 74, 222, 128; /* green-400 */

  /* Estados de Feedback */
  --color-success: 34, 197, 94; /* green-500 */
  --color-warning: 245, 158, 11; /* amber-500 */
  --color-error: 239, 68, 68; /* red-500 */
  --color-info: 59, 130, 246; /* blue-500 */

  /* Backgrounds */
  --background-primary: 255, 255, 255; /* white */
  --background-secondary: 249, 250, 251; /* gray-50 */
  --background-tertiary: 243, 244, 246; /* gray-100 */
  --background-quaternary: 229, 231, 235; /* gray-200 */

  /* Textos */
  --text-primary: 17, 24, 39; /* gray-900 */
  --text-secondary: 75, 85, 99; /* gray-600 */
  --text-tertiary: 107, 114, 128; /* gray-500 */
  --text-muted: 156, 163, 175; /* gray-400 */
  --text-inverse: 255, 255, 255; /* white */

  /* Bordas */
  --border-color: 229, 231, 235; /* gray-200 */
  --border-color-light: 243, 244, 246; /* gray-100 */
  --border-color-dark: 209, 213, 219; /* gray-300 */
  --border-focus: 59, 130, 246; /* blue-500 */

  /* Sombras */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);
}

/* ===== TEMA GLOBAL ESCURO ===== */
[data-theme="dark"] {
  /* Cores Primárias - Ajustadas para dark mode */
  --color-primary: 96, 165, 250; /* blue-400 */
  --color-primary-hover: 59, 130, 246; /* blue-500 */
  --color-primary-light: 147, 197, 253; /* blue-300 */
  --color-primary-dark: 37, 99, 235; /* blue-600 */

  /* Cores Secundárias */
  --color-secondary: 156, 163, 175; /* gray-400 */
  --color-secondary-hover: 209, 213, 219; /* gray-300 */
  --color-secondary-light: 107, 114, 128; /* gray-500 */

  /* Cores de Destaque */
  --color-accent: 74, 222, 128; /* green-400 */
  --color-accent-hover: 34, 197, 94; /* green-500 */
  --color-accent-light: 134, 239, 172; /* green-300 */

  /* Estados de Feedback */
  --color-success: 74, 222, 128; /* green-400 */
  --color-warning: 251, 191, 36; /* amber-400 */
  --color-error: 248, 113, 113; /* red-400 */
  --color-info: 96, 165, 250; /* blue-400 */

  /* Backgrounds */
  --background-primary: 17, 24, 39; /* gray-900 */
  --background-secondary: 31, 41, 55; /* gray-800 */
  --background-tertiary: 55, 65, 81; /* gray-700 */
  --background-quaternary: 75, 85, 99; /* gray-600 */

  /* Textos */
  --text-primary: 243, 244, 246; /* gray-100 */
  --text-secondary: 209, 213, 219; /* gray-300 */
  --text-tertiary: 156, 163, 175; /* gray-400 */
  --text-muted: 107, 114, 128; /* gray-500 */
  --text-inverse: 17, 24, 39; /* gray-900 */

  /* Bordas */
  --border-color: 75, 85, 99; /* gray-600 */
  --border-color-light: 55, 65, 81; /* gray-700 */
  --border-color-dark: 107, 114, 128; /* gray-500 */
  --border-focus: 96, 165, 250; /* blue-400 */

  /* Sombras - Mais intensas para dark mode */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.25), 0 2px 4px -2px rgb(0 0 0 / 0.25);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.25), 0 4px 6px -4px rgb(0 0 0 / 0.25);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.3), 0 10px 10px -5px rgb(0 0 0 / 0.15);
}

/* ========================================
   TEMAS POR PERSONA COGNITIVA
   ======================================== */

/* ===== PERSONA ADHD - Verde Calmante para Foco ===== */
[data-persona="ADHD"] {
  /* Verde para foco e calma - baseado em pesquisa científica */
  --color-primary: 34, 197, 94; /* green-500 - Melhora foco */
  --color-primary-hover: 22, 163, 74; /* green-600 */
  --color-primary-light: 74, 222, 128; /* green-400 */
  --color-primary-dark: 21, 128, 61; /* green-700 */

  /* Azul para destaque sem overstimulation */
  --color-accent: 59, 130, 246; /* blue-500 - Destaque calmo */
  --color-accent-hover: 37, 99, 235; /* blue-600 */
  --color-accent-light: 96, 165, 250; /* blue-400 */

  /* Estados de feedback com cores calmantes */
  --color-success: 34, 197, 94; /* green-500 */
  --color-warning: 245, 158, 11; /* amber-500 */
  --color-error: 220, 38, 127; /* pink-600 - Menos agressivo que vermelho */

  /* Foco em verde para consistência */
  --border-focus: 34, 197, 94; /* green-500 */

  /* Background verde muito suave para calma */
  --background-primary: 240, 253, 244; /* green-50 - Calmante */
  --background-secondary: 220, 252, 231; /* green-100 */
  --background-tertiary: 187, 247, 208; /* green-200 */

  /* Texto com alto contraste */
  --text-primary: 20, 83, 45; /* green-900 */
  --text-secondary: 22, 101, 52; /* green-800 */
}

[data-persona="ADHD"][data-theme="dark"] {
  /* ADHD dark mode - Verde calmante */
  --color-primary: 74, 222, 128; /* green-400 */
  --color-primary-hover: 34, 197, 94; /* green-500 */
  --color-primary-light: 134, 239, 172; /* green-300 */
  --color-primary-dark: 22, 163, 74; /* green-600 */

  --color-accent: 96, 165, 250; /* blue-400 */
  --color-accent-hover: 59, 130, 246; /* blue-500 */
  --color-accent-light: 147, 197, 253; /* blue-300 */

  --color-success: 74, 222, 128; /* green-400 */
  --color-warning: 251, 191, 36; /* amber-400 */
  --color-error: 244, 114, 182; /* pink-400 */

  --border-focus: 74, 222, 128; /* green-400 */

  /* Background escuro com tint verde sutil */
  --background-primary: 20, 83, 45; /* green-900 */
  --background-secondary: 22, 101, 52; /* green-800 */
  --background-tertiary: 21, 128, 61; /* green-700 */

  /* Texto claro com bom contraste */
  --text-primary: 240, 253, 244; /* green-50 */
  --text-secondary: 220, 252, 231; /* green-100 */
}

/* ===== PERSONA AUTISM - Pastéis Calmantes e Sensory-Friendly ===== */
[data-persona="AUTISM"] {
  /* Cores neutras e pastéis para reduzir sobrecarga sensorial */
  --color-primary: 148, 163, 184; /* slate-400 - Mais suave que slate-500 */
  --color-primary-hover: 100, 116, 139; /* slate-500 */
  --color-primary-light: 203, 213, 225; /* slate-300 */
  --color-primary-dark: 71, 85, 105; /* slate-600 */

  /* Accent em lavanda muito suave */
  --color-accent: 167, 139, 250; /* violet-400 - Pastel suave */
  --color-accent-hover: 139, 92, 246; /* violet-500 */
  --color-accent-light: 196, 181, 253; /* violet-300 */

  /* Estados muito suaves para evitar alarme */
  --color-success: 134, 239, 172; /* green-300 - Suave */
  --color-warning: 253, 230, 138; /* amber-200 - Muito suave */
  --color-error: 252, 165, 165; /* red-300 - Não agressivo */

  /* Foco muito sutil */
  --border-focus: 203, 213, 225; /* slate-300 */

  /* Background quase branco para calma */
  --background-primary: 248, 250, 252; /* slate-50 */
  --background-secondary: 241, 245, 249; /* slate-100 */
  --background-tertiary: 226, 232, 240; /* slate-200 */

  /* Texto com contraste moderado (não máximo) */
  --text-primary: 51, 65, 85; /* slate-700 */
  --text-secondary: 100, 116, 139; /* slate-500 */
}

[data-persona="AUTISM"][data-theme="dark"] {
  /* AUTISM dark mode - Pastéis escuros e calmantes */
  --color-primary: 203, 213, 225; /* slate-300 - Suave no escuro */
  --color-primary-hover: 148, 163, 184; /* slate-400 */
  --color-primary-light: 226, 232, 240; /* slate-200 */
  --color-primary-dark: 100, 116, 139; /* slate-500 */

  --color-accent: 196, 181, 253; /* violet-300 - Pastel escuro */
  --color-accent-hover: 167, 139, 250; /* violet-400 */
  --color-accent-light: 221, 214, 254; /* violet-200 */

  /* Estados suaves no escuro */
  --color-success: 134, 239, 172; /* green-300 */
  --color-warning: 253, 230, 138; /* amber-200 */
  --color-error: 252, 165, 165; /* red-300 */

  --border-focus: 203, 213, 225; /* slate-300 */

  /* Background escuro mas não muito contrastante */
  --background-primary: 30, 41, 59; /* slate-800 - Menos intenso */
  --background-secondary: 51, 65, 85; /* slate-700 */
  --background-tertiary: 71, 85, 105; /* slate-600 */

  /* Texto suave no escuro */
  --text-primary: 241, 245, 249; /* slate-100 */
  --text-secondary: 203, 213, 225; /* slate-300 */
}

/* ===== PERSONA DYSLEXIA - Alto Contraste e Legibilidade Máxima ===== */
[data-persona="DYSLEXIA"] {
  /* Azul para navegação - facilita orientação */
  --color-primary: 59, 130, 246; /* blue-500 - Navegação clara */
  --color-primary-hover: 37, 99, 235; /* blue-600 */
  --color-primary-light: 147, 197, 253; /* blue-300 */
  --color-primary-dark: 29, 78, 216; /* blue-700 */

  /* Âmbar para destaque - mas não muito vibrante */
  --color-accent: 245, 158, 11; /* amber-500 - Destaque sem fadiga */
  --color-accent-hover: 217, 119, 6; /* amber-600 */
  --color-accent-light: 251, 191, 36; /* amber-400 */

  /* Estados com cores distintas e alto contraste */
  --color-success: 34, 197, 94; /* green-500 */
  --color-warning: 245, 158, 11; /* amber-500 */
  --color-error: 220, 38, 127; /* pink-600 - Menos agressivo que vermelho */

  /* Foco em azul para consistência */
  --border-focus: 59, 130, 246; /* blue-500 */

  /* Background creme suave - reduz brilho e fadiga visual */
  --background-primary: 254, 252, 232; /* amber-50 - Creme muito suave */
  --background-secondary: 253, 230, 138; /* amber-100 */
  --background-tertiary: 252, 211, 77; /* amber-200 */

  /* Texto com máximo contraste para legibilidade */
  --text-primary: 17, 24, 39; /* gray-900 - Máximo contraste */
  --text-secondary: 55, 65, 81; /* gray-700 */
}

[data-persona="DYSLEXIA"][data-theme="dark"] {
  /* DYSLEXIA dark mode - Alto contraste mantido */
  --color-primary: 147, 197, 253; /* blue-300 - Azul suave no escuro */
  --color-primary-hover: 96, 165, 250; /* blue-400 */
  --color-primary-light: 191, 219, 254; /* blue-200 */
  --color-primary-dark: 59, 130, 246; /* blue-500 */

  --color-accent: 251, 191, 36; /* amber-400 - Destaque no escuro */
  --color-accent-hover: 245, 158, 11; /* amber-500 */
  --color-accent-light: 254, 215, 170; /* amber-200 */

  --color-success: 74, 222, 128; /* green-400 */
  --color-warning: 251, 191, 36; /* amber-400 */
  --color-error: 244, 114, 182; /* pink-400 */

  --border-focus: 147, 197, 253; /* blue-300 */

  /* Background escuro quente - menos agressivo que preto puro */
  --background-primary: 68, 64, 60; /* stone-600 - Escuro quente */
  --background-secondary: 87, 83, 78; /* stone-500 */
  --background-tertiary: 120, 113, 108; /* stone-400 */

  /* Texto claro com excelente contraste */
  --text-primary: 250, 250, 249; /* stone-50 */
  --text-secondary: 214, 211, 209; /* stone-300 */
}

/* Aplicação das variáveis */
body {
  background-color: rgb(var(--background-primary));
  color: rgb(var(--text-primary));
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* ========================================
   CLASSES UTILITÁRIAS DO SISTEMA DE TEMAS
   ======================================== */

@layer components {
  /* ===== BACKGROUNDS ===== */
  .bg-theme-primary {
    background-color: rgb(var(--background-primary));
  }

  .bg-theme-secondary {
    background-color: rgb(var(--background-secondary));
  }

  .bg-theme-tertiary {
    background-color: rgb(var(--background-tertiary));
  }

  .bg-theme-quaternary {
    background-color: rgb(var(--background-quaternary));
  }

  /* ===== TEXTOS ===== */
  .text-theme-primary {
    color: rgb(var(--text-primary));
  }

  .text-theme-secondary {
    color: rgb(var(--text-secondary));
  }

  .text-theme-tertiary {
    color: rgb(var(--text-tertiary));
  }

  .text-theme-muted {
    color: rgb(var(--text-muted));
  }

  .text-theme-inverse {
    color: rgb(var(--text-inverse));
  }

  /* ===== BORDAS ===== */
  .border-theme {
    border-color: rgb(var(--border-color));
  }

  .border-theme-light {
    border-color: rgb(var(--border-color-light));
  }

  .border-theme-dark {
    border-color: rgb(var(--border-color-dark));
  }

  .border-theme-focus {
    border-color: rgb(var(--border-focus));
  }

  /* ===== BOTÕES ===== */
  .btn-primary {
    background-color: rgb(var(--color-primary));
    color: rgb(var(--text-inverse));
    border: 1px solid rgb(var(--color-primary));
    transition: all 0.2s ease;
  }

  .btn-primary:hover {
    background-color: rgb(var(--color-primary-hover));
    border-color: rgb(var(--color-primary-hover));
  }

  .btn-primary:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .btn-secondary {
    background-color: transparent;
    color: rgb(var(--color-primary));
    border: 1px solid rgb(var(--color-primary));
    transition: all 0.2s ease;
  }

  .btn-secondary:hover {
    background-color: rgb(var(--color-primary));
    color: rgb(var(--text-inverse));
  }

  .btn-secondary:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .btn-accent {
    background-color: rgb(var(--color-accent));
    color: rgb(var(--text-inverse));
    border: 1px solid rgb(var(--color-accent));
    transition: all 0.2s ease;
  }

  .btn-accent:hover {
    background-color: rgb(var(--color-accent-hover));
    border-color: rgb(var(--color-accent-hover));
  }

  /* ===== ESTADOS DE FEEDBACK ===== */
  .btn-success {
    background-color: rgb(var(--color-success));
    color: rgb(var(--text-inverse));
    border: 1px solid rgb(var(--color-success));
  }

  .btn-warning {
    background-color: rgb(var(--color-warning));
    color: rgb(var(--text-inverse));
    border: 1px solid rgb(var(--color-warning));
  }

  .btn-error {
    background-color: rgb(var(--color-error));
    color: rgb(var(--text-inverse));
    border: 1px solid rgb(var(--color-error));
  }

  /* ===== CARDS ===== */
  .card-theme {
    background-color: rgb(var(--background-primary));
    border: 1px solid rgb(var(--border-color));
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease;
  }

  .card-theme:hover {
    box-shadow: var(--shadow-md);
  }

  .card-theme-elevated {
    background-color: rgb(var(--background-primary));
    border: 1px solid rgb(var(--border-color));
    box-shadow: var(--shadow-lg);
  }

  /* Garantir que cards tenham fundo visível no modo escuro */
  [data-theme="dark"] .card-theme {
    background-color: rgb(var(--background-secondary));
    border-color: rgb(var(--border-color));
  }

  /* ===== INPUTS ===== */
  .input-theme {
    background-color: rgb(var(--background-primary));
    border: 1px solid rgb(var(--border-color));
    color: rgb(var(--text-primary));
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
  }

  .input-theme:focus {
    border-color: rgb(var(--border-focus));
    outline: none;
    box-shadow: 0 0 0 3px rgb(var(--border-focus) / 0.1);
  }

  .input-theme::placeholder {
    color: rgb(var(--text-muted));
  }

  /* ===== ESTADOS DE FEEDBACK PARA INPUTS ===== */
  .input-success {
    border-color: rgb(var(--color-success));
  }

  /* ===== CHAT ESPECÍFICO ===== */
  .chat-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .chat-messages {
    flex: 1;
    overflow-y: auto;
    scroll-behavior: smooth;
    /* Scrollbar customizada */
    scrollbar-width: thin;
    scrollbar-color: rgb(var(--text-muted)) rgb(var(--background-secondary));
  }

  .chat-bubble {
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  /* ===== ARTEFATOS ===== */
  .artifact-container {
    transition: all 0.3s ease;
  }

  .artifact-header {
    backdrop-filter: blur(8px);
  }

  .artifact-content {
    position: relative;
  }

  .artifact-renderer {
    background: var(--bg-primary);
  }

  /* Layout responsivo para artefatos */
  .chat-layout-desktop {
    display: flex;
    height: 100%;
  }

  .chat-layout-mobile {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .chat-layout-single {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .chat-layout-fullscreen {
    position: relative;
    height: 100%;
  }

  /* Seções do layout */
  .chat-section {
    display: flex;
    flex-direction: column;
    min-height: 0;
    overflow: hidden;
  }

  .artifact-section {
    display: flex;
    flex-direction: column;
    min-height: 0;
    overflow: hidden;
  }

  /* Garantir scroll nas mensagens */
  .chat-section .chat-messages {
    flex: 1;
    overflow-y: auto;
    scroll-behavior: smooth;
    min-height: 0;
  }

  /* Webkit scrollbar para chat-messages */
  .chat-messages::-webkit-scrollbar {
    width: 8px;
  }

  .chat-messages::-webkit-scrollbar-track {
    background: rgb(var(--background-secondary));
    border-radius: 4px;
  }

  .chat-messages::-webkit-scrollbar-thumb {
    background: rgb(var(--text-muted));
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }

  .chat-messages::-webkit-scrollbar-thumb:hover {
    background: rgb(var(--text-secondary));
  }

  /* Otimizações para mobile */
  @media (max-width: 768px) {
    .chat-layout-desktop {
      flex-direction: column;
    }

    .chat-messages {
      padding: 0.5rem;
    }

    .chat-bubble {
      max-width: 90%;
    }

    .artifact-container {
      border-radius: 0;
    }
  }

  /* Otimizações para telas pequenas */
  @media (max-width: 640px) {
    .artifact-header {
      padding: 0.75rem;
    }

    .artifact-toolbar button {
      padding: 0.375rem;
    }
  }

  .input-warning {
    border-color: rgb(var(--color-warning));
  }

  .input-error {
    border-color: rgb(var(--color-error));
  }
}

/* Configurações específicas por Persona */

/* ADHD - Focado, visual, alto contraste */
[data-persona="ADHD"] {
  --adhd-button-size: 44px;
}

[data-persona="ADHD"] .btn-primary,
[data-persona="ADHD"] .btn-secondary {
  min-height: var(--adhd-button-size);
  min-width: var(--adhd-button-size);
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border-radius: 0.5rem;
}

[data-persona="ADHD"] .card-theme {
  border-radius: 0.75rem;
  border-width: 2px;
}

[data-persona="ADHD"] .focus-ring:focus {
  box-shadow: 0 0 0 4px rgb(var(--color-primary) / 0.3);
}

/* AUTISM - Consistente, previsível, estruturado */
[data-persona="AUTISM"] {
  --autism-border-radius: 0.25rem;
}

[data-persona="AUTISM"] .btn-primary,
[data-persona="AUTISM"] .btn-secondary {
  border-radius: var(--autism-border-radius);
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: none;
}

[data-persona="AUTISM"] .card-theme {
  border-radius: var(--autism-border-radius);
  border-width: 1px;
  transition: none;
}

[data-persona="AUTISM"] .input-theme {
  border-radius: var(--autism-border-radius);
  transition: border-color 0.1s ease;
}

[data-persona="AUTISM"] .no-animations * {
  animation: none !important;
  transition: none !important;
}

/* DYSLEXIA - Legível, suporte de áudio, espaçamento */
[data-persona="DYSLEXIA"] {
  font-family: 'OpenDyslexic', -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.8;
  letter-spacing: 0.05em;
  word-spacing: 0.1em;
}

[data-persona="DYSLEXIA"] .btn-primary,
[data-persona="DYSLEXIA"] .btn-secondary {
  padding: 0.75rem 1.25rem;
  font-size: 1.1rem;
  line-height: 1.6;
  letter-spacing: 0.025em;
  border-radius: 0.5rem;
}

[data-persona="DYSLEXIA"] .card-theme {
  padding: 1.5rem;
  line-height: 1.8;
}

[data-persona="DYSLEXIA"] .text-content {
  max-width: 65ch;
  font-size: 1.1rem;
}

/* Estilos para fonte OpenDyslexic (mantido para compatibilidade) */
.font-dyslexic {
  font-family: 'OpenDyslexic', sans-serif;
}

/* Ajuste para espaçamento entre letras e palavras para ajudar na legibilidade */
.character-spacing-wide {
  letter-spacing: 0.05em;
}

.word-spacing-wide {
  word-spacing: 0.1em;
}

.line-height-increased {
  line-height: 1.8;
}

/* ========================================
   ANIMAÇÕES PERSONALIZADAS
   ======================================== */

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-slideInRight {
  animation: slideInRight 0.4s ease-out forwards;
}

.animate-pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce-gentle {
  animation: bounce 2s infinite;
}

/* Gradientes personalizados */
.gradient-primary {
  background: linear-gradient(135deg, rgb(var(--color-primary)) 0%, rgb(var(--color-accent)) 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, rgb(var(--color-secondary)) 0%, rgb(var(--color-primary)) 100%);
}

/* ========================================
   SCROLLBAR CUSTOMIZADA
   ======================================== */

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgb(var(--background-secondary));
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgb(var(--text-muted));
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgb(var(--text-secondary));
}

.custom-scrollbar::-webkit-scrollbar-corner {
  background: rgb(var(--background-secondary));
}

/* Ajuste para tornar botões maiores - acessibilidade */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* Animações suaves para mudanças de tema */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Desabilitar animações para persona AUTISM quando necessário */
[data-persona="AUTISM"] .no-animations,
[data-persona="AUTISM"] .no-animations * {
  transition: none !important;
  animation: none !important;
}

/* Melhorias de contraste e legibilidade */
.high-contrast {
  filter: contrast(1.2);
}

.reduce-motion {
  animation: none !important;
  transition: none !important;
}

/* Estados de foco melhorados */
.focus-visible:focus-visible {
  outline: 2px solid rgb(var(--border-focus));
  outline-offset: 2px;
}

/* Indicadores visuais para diferentes estados */
.loading-state {
  opacity: 0.7;
  pointer-events: none;
}

.error-state {
  border-color: #ef4444;
  background-color: #fef2f2;
}

[data-theme="dark"] .error-state {
  border-color: #f87171;
  background-color: #7f1d1d;
}

/* ========================================
   LAYOUT PARA PÁGINAS COM HEADER FIXO
   ======================================== */

/* ========================================
   SISTEMA DE LAYOUTS UNIFICADO
   ======================================== */

/* Layout base da aplicação */
.app-layout {
  position: relative;
}

/* Headers unificados */
.app-header {
  height: 4.5rem; /* 72px - altura fixa consistente */
  z-index: 50;
}

.app-header--simple,
.app-header--authenticated {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
}

/* Main content com offset para header fixo */
.app-main--with-header {
  padding-top: 4.5rem; /* Mesmo valor da altura do header */
}

/* Controles do header */
.header-control {
  display: flex;
  align-items: center;
}

/* Estilos específicos para botões no header */
.app-header .header-control button,
.header-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: rgba(var(--background-secondary), 0.9);
  border: 1px solid rgb(var(--border-color));
  border-radius: 0.5rem;
  color: rgb(var(--text-primary));
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
  min-height: 2.5rem;
}

.app-header .header-control button:hover,
.header-button:hover {
  background-color: rgb(var(--background-tertiary));
  border-color: rgb(var(--border-color-dark));
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.app-header .header-control button:active,
.header-button:active {
  transform: translateY(0);
}

.app-header .header-control button:focus,
.header-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgb(var(--color-primary));
}

/* Texto dos botões do header */
.app-header .header-control button span,
.header-button span {
  color: rgb(var(--text-primary));
  font-weight: 500;
}

/* Ícones dos botões do header */
.app-header .header-control button svg,
.header-button svg {
  color: rgb(var(--text-secondary));
}

.app-header .header-control button:hover svg,
.header-button:hover svg {
  color: rgb(var(--text-primary));
}

/* Layout de autenticação */
.auth-layout .auth-content {
  min-height: calc(100vh - 4.5rem); /* Altura total menos header */
}

/* Layout do dashboard */
.dashboard-layout .dashboard-content {
  padding: 2rem 1rem;
  max-width: 80rem; /* max-w-6xl */
  margin: 0 auto;
}

/* Responsividade para mobile */
@media (max-width: 640px) {
  .app-header {
    height: 4rem; /* 64px em mobile */
  }
  
  .app-main--with-header {
    padding-top: 4rem;
  }
  
  .auth-layout .auth-content {
    min-height: calc(100vh - 4rem);
    padding: 1rem;
  }
  
  .dashboard-layout .dashboard-content {
    padding: 1rem 0.5rem;
  }
}

/* Manter classe legacy temporariamente para compatibilidade */
.page-with-fixed-header {
  padding-top: 4.5rem;
}

.page-with-fixed-header main {
  padding-top: 1rem;
}
