import React from 'react';
import { AppHeader } from '@/components/navigation/AppHeader';
import { AppFooter } from '@/components/navigation/AppFooter';

interface AppLayoutProps {
  children: React.ReactNode;
  showHeader?: boolean;
  showFooter?: boolean;
  headerVariant?: 'default' | 'simple' | 'authenticated';
  className?: string;
}

export const AppLayout: React.FC<AppLayoutProps> = ({
  children,
  showHeader = true,
  showFooter = false,
  headerVariant = 'default',
  className = '',
}) => {
  return (
    <div className={`app-layout min-h-screen bg-theme-primary flex flex-col ${className}`}>
      {showHeader && <AppHeader variant={headerVariant} />}
      
      <main className={`app-main flex-1 ${showHeader ? 'app-main--with-header' : ''}`}>
        {children}
      </main>
      
      {showFooter && <AppFooter />}
    </div>
  );
};