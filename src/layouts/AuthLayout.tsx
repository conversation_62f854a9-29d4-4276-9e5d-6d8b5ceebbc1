import React from 'react';
import { AppLayout } from './AppLayout';

interface AuthLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export const AuthLayout: React.FC<AuthLayoutProps> = ({
  children,
  className = '',
}) => {
  return (
    <AppLayout 
      showHeader={true}
      showFooter={false}
      headerVariant="simple"
      className={`auth-layout ${className}`}
    >
      <div className="auth-content min-h-screen flex items-center justify-center px-4 py-8">
        {children}
      </div>
    </AppLayout>
  );
};