import React from 'react';
import { AppLayout } from './AppLayout';

interface DashboardLayoutProps {
  children: React.ReactNode;
  showFooter?: boolean;
  contentClassName?: string;
  className?: string;
}

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
  showFooter = false,
  contentClassName = '',
  className = '',
}) => {
  return (
    <AppLayout 
      showHeader={true}
      showFooter={showFooter}
      headerVariant="authenticated"
      className={`dashboard-layout ${className}`}
    >
      <div className={`dashboard-content ${contentClassName}`}>
        {children}
      </div>
    </AppLayout>
  );
};