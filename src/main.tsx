
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import DebugApp from './DebugApp';
import DiagnosticApp from './DiagnosticApp';
import { registerServiceWorker } from './services/serviceWorker';

import './index.css'; // Caso exista um arquivo de estilos global

// Debug modes
const isDevelopment = import.meta.env.DEV;
const useDebugMode = window.location.search.includes('debug=true');
const useDiagnosticMode = window.location.search.includes('diagnostic=true');

// Log de inicialização para debug de redirecionamentos
console.log("🚀 [main.tsx] Aplicação iniciando", {
  currentURL: window.location.href,
  pathname: window.location.pathname,
  search: window.location.search,
  isDevelopment,
  useDebugMode,
  useDiagnosticMode
});

// Detectar redirecionamentos automáticos
const originalPushState = history.pushState;
const originalReplaceState = history.replaceState;

history.pushState = function(...args) {
  console.log("📍 [History] pushState chamado:", args[2]);
  return originalPushState.apply(this, args);
};

history.replaceState = function(...args) {
  console.log("🔄 [History] replaceState chamado:", args[2]);
  return originalReplaceState.apply(this, args);
};

const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error("Não foi possível encontrar o elemento raiz para montar a aplicação");
}

const root = ReactDOM.createRoot(rootElement);

// Renderizar componente baseado no modo
const AppComponent = 
  useDiagnosticMode ? DiagnosticApp :
  useDebugMode ? DebugApp : 
  App;

// Função para inicializar a aplicação
async function initializeApp() {
  // Registrar Service Worker em produção para resolver CORS
  if (!isDevelopment) {
    console.log('🚀 [App] Inicializando Service Worker...');
    await registerServiceWorker();
    console.log('✅ [App] Service Worker inicializado');
  }

  // Renderizar aplicação
  root.render(
    <React.StrictMode>
      <AppComponent />
    </React.StrictMode>
  );
}

// Inicializar aplicação
initializeApp().catch(console.error);