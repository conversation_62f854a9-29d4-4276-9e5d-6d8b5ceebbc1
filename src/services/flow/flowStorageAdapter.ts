/**
 * Adaptador para armazenamento de sessões do Flow no Firebase
 */
import {
  collection,
  doc,
  setDoc,
  getDoc,
  query,
  where,
  getDocs,
  orderBy,
  limit,
  Timestamp,
  deleteDoc
} from 'firebase/firestore';
import { db } from '@/services/firebase';
import { FlowSession, FlowConfig, ChatMessage } from '@/types/flow';

export class FlowStorageAdapter {
  private readonly collectionName = 'flowSessions';

  /**
   * Remove campos undefined de um objeto para compatibilidade com Firestore
   */
  private cleanObject<T extends Record<string, any>>(obj: T): Partial<T> {
    const cleaned: Partial<T> = {};
    for (const [key, value] of Object.entries(obj)) {
      if (value !== undefined) {
        cleaned[key as keyof T] = value;
      }
    }
    return cleaned;
  }
  
  /**
   * Salva uma sessão do Flow no Firestore
   */
  public async saveSession(session: Omit<FlowSession, 'id' | 'createdAt'>): Promise<FlowSession> {
    try {
      console.log('🔧 [FlowStorageAdapter] Tentando salvar sessão:', {
        userId: session.userId,
        modelId: session.modelId,
        hasFlowConfig: !!session.flowConfig
      });

      const sessionDoc = doc(collection(db, this.collectionName));

      const createdAt = Timestamp.now();
      const flowSession: FlowSession = {
        ...session,
        id: sessionDoc.id,
        createdAt: createdAt.toDate()
      };

      // Converter Date para Timestamp para armazenamento no Firestore
      const sessionForFirestore = {
        ...flowSession,
        createdAt,
        lastUsedAt: Timestamp.fromDate(flowSession.lastUsedAt),
        flowConfig: this.cleanObject(flowSession.flowConfig), // Remove campos undefined
      };

      console.log('📝 [FlowStorageAdapter] Dados para Firestore:', {
        id: sessionDoc.id,
        userId: sessionForFirestore.userId,
        modelId: sessionForFirestore.modelId,
        collectionName: this.collectionName
      });

      await setDoc(sessionDoc, sessionForFirestore);

      console.log('✅ [FlowStorageAdapter] Sessão salva com sucesso:', sessionDoc.id);
      return flowSession;
    } catch (error) {
      console.error('❌ [FlowStorageAdapter] Erro ao salvar sessão do Flow:', error);
      throw new Error(`Erro ao salvar sessão do Flow: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }
  
  /**
   * Atualiza uma sessão existente do Flow
   */
  public async updateSession(sessionId: string, updates: Partial<Omit<FlowSession, 'id' | 'createdAt'>>): Promise<void> {
    try {
      const sessionRef = doc(db, this.collectionName, sessionId);
      const sessionDoc = await getDoc(sessionRef);
      
      if (!sessionDoc.exists()) {
        throw new Error(`Sessão com ID ${sessionId} não encontrada`);
      }
      
      // Preparar dados para update
      const updateData: Record<string, any> = {};
      if (updates.userId) updateData.userId = updates.userId;
      if (updates.name !== undefined) updateData.name = updates.name; // Permite string vazia
      if (updates.modelId) updateData.modelId = updates.modelId;
      if (updates.flowConfig) updateData.flowConfig = this.cleanObject(updates.flowConfig);
      if (updates.messages) updateData.messages = updates.messages;
      if (updates.lastUsedAt) updateData.lastUsedAt = Timestamp.fromDate(updates.lastUsedAt);
      
      await setDoc(sessionRef, updateData, { merge: true });
    } catch (error) {
      console.error('Erro ao atualizar sessão do Flow:', error);
      throw new Error(`Erro ao atualizar sessão do Flow: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }
  
  /**
   * Recupera uma sessão do Flow por ID
   */
  public async getSession(sessionId: string): Promise<FlowSession | null> {
    try {
      const sessionRef = doc(db, this.collectionName, sessionId);
      const sessionDoc = await getDoc(sessionRef);
      
      if (!sessionDoc.exists()) {
        return null;
      }
      
      const data = sessionDoc.data();
      
      // Converter Timestamps para Date
      return {
        id: sessionDoc.id,
        userId: data.userId,
        name: data.name,
        modelId: data.modelId,
        createdAt: data.createdAt.toDate(),
        lastUsedAt: data.lastUsedAt.toDate(),
        flowConfig: data.flowConfig,
        messages: data.messages || [],
      };
    } catch (error) {
      console.error('Erro ao recuperar sessão do Flow:', error);
      throw new Error(`Erro ao recuperar sessão do Flow: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }
  
  /**
   * Busca sessões do Flow por usuário
   */
  public async getSessionsByUser(userId: string, limitCount = 10): Promise<FlowSession[]> {
    try {
      // Query simples sem orderBy para evitar necessidade de índice composto
      const sessionsQuery = query(
        collection(db, this.collectionName),
        where('userId', '==', userId)
      );

      const querySnapshot = await getDocs(sessionsQuery);
      const sessions: FlowSession[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        sessions.push({
          id: doc.id,
          userId: data.userId,
          name: data.name,
          modelId: data.modelId,
          createdAt: data.createdAt.toDate(),
          lastUsedAt: data.lastUsedAt.toDate(),
          flowConfig: data.flowConfig,
          messages: data.messages || [],
        });
      });

      // Ordenar no cliente e aplicar limite
      return sessions
        .sort((a, b) => b.lastUsedAt.getTime() - a.lastUsedAt.getTime())
        .slice(0, limitCount);
    } catch (error) {
      console.error('Erro ao buscar sessões do Flow por usuário:', error);
      throw new Error(`Erro ao buscar sessões do Flow: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }
  
  /**
   * Exclui uma sessão do Flow
   */
  public async deleteSession(sessionId: string): Promise<void> {
    try {
      await deleteDoc(doc(db, this.collectionName, sessionId));
    } catch (error) {
      console.error('Erro ao excluir sessão do Flow:', error);
      throw new Error(`Erro ao excluir sessão do Flow: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }
  
  /**
   * Adiciona mensagem a uma sessão existente
   */
  public async addMessageToSession(sessionId: string, message: ChatMessage): Promise<void> {
    try {
      const sessionRef = doc(db, this.collectionName, sessionId);
      const sessionDoc = await getDoc(sessionRef);
      
      if (!sessionDoc.exists()) {
        throw new Error(`Sessão com ID ${sessionId} não encontrada`);
      }
      
      const data = sessionDoc.data();
      const messages = data.messages || [];
      
      // Atualizar sessão com nova mensagem e timestamp
      await setDoc(sessionRef, {
        messages: [...messages, message],
        lastUsedAt: Timestamp.now()
      }, { merge: true });
      
    } catch (error) {
      console.error('Erro ao adicionar mensagem à sessão:', error);
      throw new Error(`Erro ao adicionar mensagem à sessão: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }
  
  /**
   * Salva configuração do Flow para um usuário
   */
  public async saveFlowConfig(userId: string, config: FlowConfig): Promise<void> {
    try {
      const configRef = doc(db, 'flowConfigs', userId);
      await setDoc(configRef, {
        ...this.cleanObject(config),
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Erro ao salvar configuração do Flow:', error);
      throw new Error(`Erro ao salvar configuração do Flow: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }
  
  /**
   * Recupera configuração do Flow para um usuário
   */
  public async getFlowConfig(userId: string): Promise<FlowConfig | null> {
    try {
      const configRef = doc(db, 'flowConfigs', userId);
      const configDoc = await getDoc(configRef);

      if (!configDoc.exists()) {
        return null;
      }

      const data = configDoc.data();
      return {
        flowBaseUrl: data.flowBaseUrl,
        flowTenant: data.flowTenant,
        flowClientId: data.flowClientId,
        flowClientSecret: data.flowClientSecret,
        flowAppToAccess: data.flowAppToAccess,
        flowAgent: data.flowAgent,
        flowAuthBaseUrl: data.flowAuthBaseUrl,
        apiModelId: data.apiModelId,
        modelTemperature: data.modelTemperature
      };
    } catch (error) {
      console.error('Erro ao recuperar configuração do Flow:', error);
      throw new Error(`Erro ao recuperar configuração do Flow: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }

  /**
   * Remove configuração do Flow para um usuário
   */
  public async deleteFlowConfig(userId: string): Promise<void> {
    try {
      const configRef = doc(db, 'flowConfigs', userId);
      await deleteDoc(configRef);
      console.log(`✅ [FlowStorageAdapter] Configuração do Flow removida para usuário: ${userId}`);
    } catch (error) {
      console.error('Erro ao remover configuração do Flow:', error);
      throw new Error(`Erro ao remover configuração do Flow: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }
}

// Instância singleton para uso em toda a aplicação
export const flowStorageService = new FlowStorageAdapter();
