/**
 * Service Worker para resolver problemas de CORS
 */

export async function registerServiceWorker(): Promise<void> {
  if (!('serviceWorker' in navigator)) {
    console.warn('⚠️ [SW] Service Worker não suportado neste navegador');
    return;
  }

  try {
    console.log('🔧 [SW] Registrando Service Worker...');

    // <PERSON><PERSON>, desregistrar qualquer SW antigo
    const existingRegistrations = await navigator.serviceWorker.getRegistrations();
    for (const registration of existingRegistrations) {
      console.log('🗑️ [SW] Removendo Service Worker antigo');
      await registration.unregister();
    }

    const registration = await navigator.serviceWorker.register('/sw.js', {
      scope: '/',
      updateViaCache: 'none' // Sempre buscar nova versão
    });

    console.log('✅ [SW] Service Worker registrado com sucesso:', registration.scope);

    // Aguardar o Service Worker estar completamente ativo
    await ensureServiceWorkerActive(registration);

    // Verificar se o SW está controlando a página
    if (!navigator.serviceWorker.controller) {
      console.log('🔄 [SW] Aguardando controle da página...');
      await new Promise<void>((resolve) => {
        navigator.serviceWorker.addEventListener('controllerchange', () => {
          console.log('✅ [SW] Service Worker agora controla a página');
          resolve();
        });
      });
    } else {
      console.log('✅ [SW] Service Worker já controla a página');
    }

    // Escutar atualizações
    registration.addEventListener('updatefound', () => {
      console.log('🔄 [SW] Nova versão do Service Worker encontrada');
      const newWorker = registration.installing;
      if (newWorker) {
        waitForServiceWorker(newWorker);
      }
    });

    // Testar se o SW está funcionando
    console.log('🧪 [SW] Testando interceptação...');
    setTimeout(() => {
      console.log('🔍 [SW] Controller atual:', navigator.serviceWorker.controller?.scriptURL);
    }, 1000);

  } catch (error) {
    console.error('❌ [SW] Erro ao registrar Service Worker:', error);
  }
}

async function ensureServiceWorkerActive(registration: ServiceWorkerRegistration): Promise<void> {
  if (registration.active) {
    console.log('✅ [SW] Service Worker já está ativo');
    return;
  }

  if (registration.installing) {
    console.log('🔄 [SW] Service Worker instalando...');
    await waitForServiceWorker(registration.installing);
  }

  if (registration.waiting) {
    console.log('⏳ [SW] Service Worker aguardando...');
    await waitForServiceWorker(registration.waiting);
  }

  // Aguardar até estar ativo
  while (!registration.active) {
    console.log('⏳ [SW] Aguardando ativação...');
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  console.log('✅ [SW] Service Worker ativo');
}

function waitForServiceWorker(worker: ServiceWorker): Promise<void> {
  return new Promise((resolve) => {
    worker.addEventListener('statechange', () => {
      console.log('🔄 [SW] Estado mudou para:', worker.state);
      if (worker.state === 'activated') {
        resolve();
      }
    });
  });
}

export async function unregisterServiceWorker(): Promise<void> {
  if (!('serviceWorker' in navigator)) {
    return;
  }

  try {
    const registrations = await navigator.serviceWorker.getRegistrations();
    
    for (const registration of registrations) {
      await registration.unregister();
      console.log('🗑️ [SW] Service Worker removido');
    }
  } catch (error) {
    console.error('❌ [SW] Erro ao remover Service Worker:', error);
  }
}
