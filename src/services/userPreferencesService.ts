import { doc, setDoc, getDoc, Timestamp } from 'firebase/firestore';
import { db } from '@/services/firebase';
import { ThemeMode } from '@/types/theme';
import { CognitiveType } from '@/types/cognitive';

export interface UserPreferences {
  theme: ThemeMode;
  persona: CognitiveType;
  updatedAt?: Date;
}

class UserPreferencesService {
  /**
   * Salva as preferências do usuário no Firestore
   */
  public async saveUserPreferences(userId: string, preferences: Omit<UserPreferences, 'updatedAt'>): Promise<void> {
    try {
      const preferencesRef = doc(db, 'userPreferences', userId);
      await setDoc(preferencesRef, {
        ...preferences,
        updatedAt: Timestamp.now()
      }, { merge: true });
      
      console.log('Preferências do usuário salvas no Firestore:', preferences);
    } catch (error) {
      console.error('Erro ao salvar preferências do usuário:', error);
      throw error;
    }
  }

  /**
   * Recupera as preferências do usuário do Firestore
   */
  public async getUserPreferences(userId: string): Promise<UserPreferences | null> {
    try {
      const preferencesRef = doc(db, 'userPreferences', userId);
      const preferencesDoc = await getDoc(preferencesRef);
      
      if (!preferencesDoc.exists()) {
        return null;
      }
      
      const data = preferencesDoc.data();
      return {
        theme: data.theme as ThemeMode,
        persona: data.persona as CognitiveType,
        updatedAt: data.updatedAt?.toDate()
      };
    } catch (error) {
      console.error('Erro ao recuperar preferências do usuário:', error);
      return null;
    }
  }

  /**
   * Atualiza apenas o tema do usuário
   */
  public async updateUserTheme(userId: string, theme: ThemeMode): Promise<void> {
    try {
      const preferencesRef = doc(db, 'userPreferences', userId);
      await setDoc(preferencesRef, {
        theme,
        updatedAt: Timestamp.now()
      }, { merge: true });
      
      console.log('Tema do usuário atualizado no Firestore:', theme);
    } catch (error) {
      console.error('Erro ao atualizar tema do usuário:', error);
      throw error;
    }
  }

  /**
   * Atualiza apenas a persona do usuário
   */
  public async updateUserPersona(userId: string, persona: CognitiveType): Promise<void> {
    try {
      const preferencesRef = doc(db, 'userPreferences', userId);
      await setDoc(preferencesRef, {
        persona,
        updatedAt: Timestamp.now()
      }, { merge: true });
      
      console.log('Persona do usuário atualizada no Firestore:', persona);
    } catch (error) {
      console.error('Erro ao atualizar persona do usuário:', error);
      throw error;
    }
  }
}

// Instância singleton
export const userPreferencesService = new UserPreferencesService();
