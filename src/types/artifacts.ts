/**
 * Tipos específicos para o sistema de artefatos
 */

import { ChatMessageArtifact } from './chat';

// Configurações de layout para artefatos
export interface ArtifactLayoutConfig {
  showArtifact: boolean;
  artifactPosition: 'right' | 'bottom'; // right para desktop, bottom para mobile
  artifactWidth?: string; // Para desktop split-screen
  isFullscreen?: boolean;
}

// Ações disponíveis para artefatos
export type ArtifactAction = 
  | 'copy'
  | 'download' 
  | 'edit'
  | 'fullscreen'
  | 'close'
  | 'version-history'
  | 'refresh';

// Contexto de um artefato para edições
export interface ArtifactContext {
  messageId: string;
  conversationHistory: string[]; // Últimas mensagens relevantes
  userIntent?: string; // Intenção detectada do usuário
}

// Critérios para criação automática de artefatos
export interface ArtifactCreationCriteria {
  minLines: number; // Mínimo de linhas para código
  minLength: number; // Mínimo de caracteres para documentos
  hasStructure: boolean; // Se tem estrutura definida (HTML, JSON, etc.)
  isStandalone: boolean; // Se é conteúdo independente
  isReusable: boolean; // Se é provável ser reutilizado
}

// Configurações padrão para detecção de artefatos
export const DEFAULT_ARTIFACT_CRITERIA: ArtifactCreationCriteria = {
  minLines: 15,
  minLength: 500,
  hasStructure: true,
  isStandalone: true,
  isReusable: true,
};

// Tipos de conteúdo que podem gerar artefatos
export const ARTIFACT_TRIGGERS = {
  CODE: ['```', 'function', 'class', 'import', 'export'],
  HTML: ['<!DOCTYPE', '<html', '<div', '<component'],
  DOCUMENT: ['# ', '## ', '### ', '**', '*', '1. ', '- '],
  SVG: ['<svg', '<path', '<circle', '<rect'],
  REACT: ['import React', 'export default', 'useState', 'useEffect'],
} as const;

// Extensões de arquivo por tipo de artefato
export const ARTIFACT_EXTENSIONS = {
  code: {
    javascript: '.js',
    typescript: '.ts',
    python: '.py',
    html: '.html',
    css: '.css',
    json: '.json',
  },
  document: '.md',
  svg: '.svg',
  react: '.tsx',
} as const;

// Estado do sistema de artefatos
export interface ArtifactState {
  activeArtifactId: string | null;
  artifacts: Map<string, ChatMessageArtifact>;
  layoutConfig: ArtifactLayoutConfig;
  isEditing: boolean;
  editingArtifactId: string | null;
}

// Ações para o reducer de artefatos
export type ArtifactActionType = 
  | { type: 'CREATE_ARTIFACT'; payload: ChatMessageArtifact }
  | { type: 'UPDATE_ARTIFACT'; payload: { id: string; updates: Partial<ChatMessageArtifact> } }
  | { type: 'DELETE_ARTIFACT'; payload: string }
  | { type: 'SET_ACTIVE_ARTIFACT'; payload: string | null }
  | { type: 'SET_LAYOUT_CONFIG'; payload: Partial<ArtifactLayoutConfig> }
  | { type: 'START_EDITING'; payload: string }
  | { type: 'STOP_EDITING' }
  | { type: 'ADD_VERSION'; payload: { artifactId: string; version: any } };
