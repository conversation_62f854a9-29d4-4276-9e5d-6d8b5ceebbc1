/**
 * Tipos relacionados ao sistema de chat
 */

// Role unificado para mensagens de chat (UI)
export type ChatMessageRole = 'user' | 'assistant' | 'system';

// Role para APIs de LLM (mais específico)
export type LLMMessageRole = 'system' | 'user' | 'assistant' | 'function' | 'tool';

// Versão de um artefato
export interface ArtifactVersion {
  id: string;
  content: string;
  createdAt: Date;
  description?: string; // Descrição da mudança
}

export interface ChatMessageArtifact {
  id: string;
  type: 'code' | 'html' | 'document' | 'svg' | 'react' | 'image';
  status: 'idle' | 'loading' | 'loaded' | 'error';
  title?: string;
  language?: string; // Para código: javascript, python, etc.
  content: string; // Conteúdo principal do artefato
  dataUrl?: string; // base64 data URL for image
  promptUsed?: string; // Prompt original ou ideia principal
  error?: string;
  createdAt: Date;
  updatedAt: Date;
  versions?: ArtifactVersion[]; // Histórico de versões
}

// Mensagem de chat para UI (versão principal)
export interface ChatMessage {
  id: string;
  role: ChatMessageRole;
  text: string;
  timestamp: Date;
  artifact?: ChatMessageArtifact;
}

// Mensagem de chat para APIs de LLM
export interface LLMMessage {
  role: LLMMessageRole;
  content: string;
  name?: string;
}