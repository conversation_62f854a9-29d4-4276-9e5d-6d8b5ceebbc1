/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  safelist: [
    // Layout essencial
    'min-h-screen', 'h-screen', 'w-full', 'h-full',
    'flex', 'flex-col', 'flex-row', 'items-center', 'justify-center', 'justify-between',
    'fixed', 'absolute', 'relative', 'static',
    'top-0', 'left-0', 'right-0', 'bottom-0',
    'z-10', 'z-20', 'z-40', 'z-50',

    // Spacing
    'p-2', 'p-3', 'p-4', 'p-6', 'p-8', 'px-4', 'px-6', 'px-8', 'py-3', 'py-4', 'py-6', 'py-8',
    'm-4', 'm-6', 'm-8', 'mx-4', 'mx-6', 'mx-8', 'my-4', 'my-6', 'my-8',
    'mx-auto', 'mt-auto', 'mb-4', 'mb-6', 'mb-8',
    'pt-20', 'pt-24', 'pt-28', 'pb-8', 'pb-12',
    'space-x-1', 'space-x-2', 'space-x-3', 'space-x-4',

    // Sizing
    'w-8', 'w-12', 'w-16', 'w-20', 'h-8', 'h-12', 'h-16', 'h-20',
    'w-5', 'h-5', 'max-w-lg', 'max-w-xl', 'max-w-2xl', 'max-w-4xl', 'max-w-7xl',

    // Typography
    'text-xs', 'text-sm', 'text-base', 'text-lg', 'text-xl', 'text-2xl', 'text-3xl',
    'font-normal', 'font-medium', 'font-semibold', 'font-bold',
    'text-center', 'text-left', 'text-right',
    'leading-tight', 'leading-normal', 'leading-relaxed',

    // Colors and themes
    'text-white', 'text-black', 'text-gray-500', 'text-gray-600', 'text-gray-700', 'text-gray-800', 'text-gray-900',
    'bg-white', 'bg-gray-50', 'bg-gray-100', 'bg-gray-200', 'bg-gray-800', 'bg-gray-900',
    'border-gray-200', 'border-gray-300', 'border-gray-400',
    'backdrop-blur-sm', 'backdrop-blur-md',

    // Borders and radius
    'border', 'border-2', 'border-t', 'border-b', 'border-l', 'border-r',
    'rounded', 'rounded-lg', 'rounded-xl', 'rounded-full',

    // Shadows
    'shadow', 'shadow-sm', 'shadow-md', 'shadow-lg', 'shadow-xl', 'shadow-2xl',
    'hover:shadow-md', 'hover:shadow-lg', 'hover:shadow-xl', 'hover:shadow-2xl',

    // Transitions and animations
    'transition', 'transition-all', 'transition-colors', 'transition-shadow',
    'duration-150', 'duration-200', 'duration-300', 'duration-500',
    'animate-spin', 'animate-pulse', 'animate-fadeInUp',

    // Interactive states
    'hover:bg-gray-50', 'hover:bg-gray-100', 'hover:text-gray-900',
    'disabled:opacity-50', 'disabled:cursor-not-allowed',
    'cursor-pointer', 'cursor-not-allowed',

    // Custom theme classes
    'bg-theme-primary', 'bg-theme-secondary', 'bg-theme-tertiary',
    'text-theme-primary', 'text-theme-secondary', 'text-theme-muted',
    'border-theme', 'border-theme-light', 'border-theme-dark',
    'card-theme', 'btn-primary', 'btn-secondary', 'input-theme'
  ],
  darkMode: ['class', '[data-theme="dark"]'],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        opendyslexic: ['OpenDyslexic', 'Verdana', 'sans-serif'],
      },
      animation: {
        shake: 'shake 0.5s cubic-bezier(.36,.07,.19,.97) both',
        fadeIn: 'fadeIn 0.3s ease-in-out forwards',
      },
      keyframes: {
        shake: {
          '10%, 90%': { transform: 'translate3d(-1px, 0, 0)' },
          '20%, 80%': { transform: 'translate3d(2px, 0, 0)' },
          '30%, 50%, 70%': { transform: 'translate3d(-4px, 0, 0)' },
          '40%, 60%': { transform: 'translate3d(4px, 0, 0)' },
        },
        fadeIn: {
          '0%': { opacity: '0', transform: 'translateY(10px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        }
      }
    }
  },
  plugins: [],
}
